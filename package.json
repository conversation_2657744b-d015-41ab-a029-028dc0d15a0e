{"name": "mobile-generate-code", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "pnpm -r build", "test": "echo \"Error: no test specified\" && exit 1", "generate": "node generate-test.js"}, "keywords": [], "author": "", "license": "ISC", "workspaces": ["packages/**", "projects/**"], "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "ts-node": "^10.9.1"}, "pnpm": {"overrides": {"@tarojs/components": "3.6.31", "@tarojs/react": "3.6.31", "@tarojs/taro": "3.6.31", "@tarojs/helper": "3.6.31", "@tarojs/plugin-framework-react": "3.6.31", "@tarojs/plugin-http": "3.6.31", "@tarojs/plugin-platform-h5": "3.6.31", "@tarojs/runtime": "3.6.31", "@tarojs/shared": "3.6.31", "@tarojs/webpack5-runner": "3.6.31", "react": "18.2.0", "react-dom": "18.2.0"}}}
import React, { useEffect } from "react";
import { useEnhancedComponent, usePageComponents, useForm, evaluateExpression } from "@lyy/core";
import { LyyContainer, LyyForm, LyyNumberInput, LyyFormula, LyyButton } from "@lyy/components";
import Taro from "@tarojs/taro";
const COMPONENT_DATA = {
  "Container1": {
    "entityId": "Container1",
    "compName": "lyy-container",
    "prop": {
      "aliasName": "\u9875\u9762c92mg",
      "isHasNav": true,
      "isPadding": false,
      "isHasTabBar": true,
      "flex": true,
      "navProps": {
        "title": "\u661F\u4E91\u5F00\u7269",
        "showBack": false,
        "showBackExp": "",
        "customBack": false,
        "customHome": false,
        "homeUrl": "",
        "children": [],
        "rightBtns": [],
        "show": {
          "exp": "1 === 1"
        }
      },
      "tabBarProps": [{
        "pagePath": "/pages/index/index",
        "text": "\u6807\u7B7E",
        "icon": "FanhuidingbuLine",
        "activeColor": "#0987F5",
        "inActiveColor": "#848F99",
        "showExp": "2 == 2",
        "authCode": ""
      }, {
        "pagePath": "/pages/my/index",
        "text": "\u6211\u7684",
        "icon": "DingweiLine",
        "activeColor": "",
        "inActiveColor": "",
        "showExp": "",
        "authCode": ""
      }]
    },
    "state": {},
    "modelValue": {}
  },
  "Form1": {
    "entityId": "Form1",
    "compName": "lyy-form",
    "prop": {
      "labelAlign": "left",
      "form": {},
      "isErpForm": false,
      "show": {
        "exp": ""
      },
      "forbid": {
        "exp": ""
      },
      "isFlow": false,
      "flowProp": {
        "field": "taskApproval",
        "html": "<p style=\"color:#666\">${createTime}</p><div style=\"display:flex;align-items:center;margin:6px 0\"><img width=\"30px\" height=\"30px\" style=\"width:30px;height:30px;margin-right:4px\" src=\"https://page-configure.oss-cn-shenzhen.aliyuncs.com/page-configure/attachment/icon/1709636619747_icon\uFF0Fcolours\uFF0F\u5934\u50CF_\u9ED8\<EMAIL>\"/><p>${createBy}<p style=\"color:#999;display:inline\">${typeStr}</p></p></div><p style=\"color:#999\">${content.opinion}</p>"
      },
      "aliasName": "\u8868\u5355ccuz8"
    },
    "state": {},
    "modelValue": {}
  },
  "NumberInput3": {
    "entityId": "NumberInput3",
    "compName": "lyy-number-input",
    "prop": {
      "field": "price",
      "label": "\u5355\u4EF7",
      "aliasName": "\u6570\u5B57\u8F93\u5165\u6846cd9hv",
      "placeholder": "\u8BF7\u8F93\u5165",
      "defaultValue": "",
      "validateTrigger": "change",
      "maxlength": null,
      "size": "default",
      "suffix": "",
      "addonAfter": "",
      "rules": [],
      "readonly": false,
      "disabled": false,
      "clearable": true,
      "formColumn": false,
      "decimalSeparator": 2,
      "icon": {
        "iconName": "",
        "popover": {
          "description": []
        }
      },
      "formId": "ccuz8ypotl",
      "show": {
        "exp": ""
      },
      "forbid": {
        "exp": ""
      }
    },
    "state": {},
    "modelValue": {}
  },
  "NumberInput2": {
    "entityId": "NumberInput2",
    "compName": "lyy-number-input",
    "prop": {
      "field": "count",
      "label": "\u6570\u91CF",
      "aliasName": "\u6570\u5B57\u8F93\u5165\u6846cv36f",
      "placeholder": "\u8BF7\u8F93\u5165",
      "defaultValue": "",
      "validateTrigger": "change",
      "maxlength": null,
      "size": "default",
      "suffix": "",
      "addonAfter": "",
      "rules": [],
      "readonly": false,
      "disabled": false,
      "clearable": true,
      "formColumn": false,
      "decimalSeparator": 2,
      "icon": {
        "iconName": "",
        "popover": {
          "description": []
        }
      },
      "formId": "ccuz8ypotl",
      "show": {
        "exp": ""
      },
      "forbid": {
        "exp": ""
      }
    },
    "state": {},
    "modelValue": {}
  },
  "Formula1": {
    "entityId": "Formula1",
    "compName": "lyy-formula",
    "prop": {
      "field": "sum",
      "label": "\u5408\u8BA1",
      "aliasName": "\u8BA1\u7B97\u503C\u7EC4\u4EF6c44f8",
      "exp": "ccuz8ypotl.modelValue.price * ccuz8ypotl.modelValue.count ",
      "placeholder": "\u8BF7\u8F93\u5165",
      "labelWidth": "",
      "labelAlign": "left",
      "defaultValue": "",
      "rules": [],
      "type": "text",
      "pipes": [{
        "pipe": "",
        "option": {}
      }],
      "show": {
        "exp": ""
      },
      "forbid": {
        "exp": ""
      },
      "formId": "ccuz8ypotl"
    },
    "state": {},
    "modelValue": {}
  },
  "Button1": {
    "entityId": "Button1",
    "compName": "lyy-button",
    "prop": {
      "text": "\u63D0\u4EA4",
      "aliasName": "\u6309\u94AEcsuug",
      "color": "primary",
      "buttonSize": "medium",
      "shape": "default",
      "variant": "default",
      "block": true,
      "disabled": false,
      "isBorderRadius": false,
      "formColumn": false,
      "subTitle": "",
      "authCode": "",
      "show": {
        "exp": ""
      },
      "forbid": {
        "exp": ""
      }
    },
    "state": {},
    "modelValue": {}
  }
};
function GeneratedPage() {
  const {
    initPage: initPage,
    components: pageComponents,
    executePageAction: executePageAction
  } = usePageComponents();
  useEffect(() => {
    initPage(COMPONENT_DATA);
  }, []);
  const {
    component: Container1_component,
    snapshot: Container1_snapshot,
    executeAction: Container1_executeAction,
    updateProp: Container1_updateProp
  } = useEnhancedComponent("Container1");
  const {
    component: Form1_component,
    snapshot: Form1_snapshot,
    executeAction: Form1_executeAction,
    updateProp: Form1_updateProp
  } = useEnhancedComponent("Form1");
  const {
    modelValue: Form1_modelValue,
    setValue: Form1_setValue,
    validateForm: Form1_validateForm
  } = useForm("Form1");
  const {
    component: NumberInput3_component,
    snapshot: NumberInput3_snapshot,
    executeAction: NumberInput3_executeAction,
    updateProp: NumberInput3_updateProp
  } = useEnhancedComponent("NumberInput3");
  const {
    component: NumberInput2_component,
    snapshot: NumberInput2_snapshot,
    executeAction: NumberInput2_executeAction,
    updateProp: NumberInput2_updateProp
  } = useEnhancedComponent("NumberInput2");
  const {
    component: Formula1_component,
    snapshot: Formula1_snapshot,
    executeAction: Formula1_executeAction,
    updateProp: Formula1_updateProp
  } = useEnhancedComponent("Formula1");
  const {
    component: Button1_component,
    snapshot: Button1_snapshot,
    executeAction: Button1_executeAction,
    updateProp: Button1_updateProp
  } = useEnhancedComponent("Button1");
  async function handle_Form1_beforeMount_0() {
    Form1_executeAction({
      "event": "beforeMount",
      "action": "request",
      "option": {
        "url": "/api/aaa",
        "method": "post",
        "responseType": "json",
        "payloads": {
          "type": "dynamic",
          "dynamic": {
            "nodePath": ""
          }
        },
        "responseDataKey": "formData",
        "customOption": {
          "loading": true,
          "nextEventsDelay": 0,
          "parse": false,
          "unhandled": false
        },
        "headerPayloads": [],
        "interceptors": {
          "requestInterceptor": "",
          "responseInterceptor": ""
        }
      },
      "thenActions": []
    }, "beforeMount");
  }
  async function handle_Button1_click_0() {
    Button1_executeAction({
      "event": "click",
      "action": "request",
      "option": {
        "url": "/api/bbbb",
        "method": "post",
        "responseType": "json",
        "payloads": {
          "type": "dynamic",
          "dynamic": {
            "nodePath": ""
          }
        },
        "responseDataKey": "",
        "customOption": {
          "loading": true,
          "nextEventsDelay": 0,
          "parse": false,
          "unhandled": false
        },
        "headerPayloads": [],
        "interceptors": {
          "requestInterceptor": "",
          "responseInterceptor": ""
        }
      },
      "thenActions": []
    }, "click");
  }
  return <LyyContainer entityId="Container1" compName="lyy-container" prop={Container1_snapshot.prop}><LyyForm entityId="Form1" compName="lyy-form" prop={Form1_snapshot.prop} onBeforeMount={handle_Form1_beforeMount_0}><LyyNumberInput entityId="NumberInput3" compName="lyy-number-input" prop={NumberInput3_snapshot.prop}></LyyNumberInput><LyyNumberInput entityId="NumberInput2" compName="lyy-number-input" prop={NumberInput2_snapshot.prop}></LyyNumberInput><LyyFormula entityId="Formula1" compName="lyy-formula" prop={Formula1_snapshot.prop}></LyyFormula><LyyButton entityId="Button1" compName="lyy-button" prop={Button1_snapshot.prop} onClick={handle_Button1_click_0}></LyyButton></LyyForm></LyyContainer>;
}
export default GeneratedPage;
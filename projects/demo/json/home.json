{"compName": "lyy-container", "compId": "c92mgf5a3d", "compNameCn": "页面", "prop": {"aliasName": "页面c92mg", "isHasNav": true, "isPadding": false, "isHasTabBar": true, "flex": true, "navProps": {"title": "星云开物", "showBack": false, "showBackExp": "", "customBack": false, "customHome": false, "homeUrl": "", "children": [], "rightBtns": [], "show": {"exp": "1 === 1"}}, "tabBarProps": [{"pagePath": "/pages/index/index", "text": "标签", "icon": "FanhuidingbuLine", "activeColor": "#0987F5", "inActiveColor": "#848F99", "showExp": "2 == 2", "authCode": ""}, {"pagePath": "/pages/my/index", "text": "我的", "icon": "DingweiLine", "activeColor": "", "inActiveColor": "", "showExp": "", "authCode": ""}]}, "actions": [], "childrens": [{"compName": "lyy-form", "compId": "ccuz8ypotl", "compNameCn": "表单", "actions": [{"event": "beforeMount", "action": "request", "option": {"url": "/api/aaa", "method": "post", "responseType": "json", "payloads": {"type": "dynamic", "dynamic": {"nodePath": ""}}, "responseDataKey": "formData", "customOption": {"loading": true, "nextEventsDelay": 0, "parse": false, "unhandled": false}, "headerPayloads": [], "interceptors": {"requestInterceptor": "", "responseInterceptor": ""}}, "thenActions": []}], "childrens": [{"compName": "lyy-number-input", "compId": "cd9hv0piuk", "compNameCn": "数字输入框", "modelValue": "", "prop": {"field": "price", "label": "单价", "aliasName": "数字输入框cd9hv", "placeholder": "请输入", "defaultValue": "", "validateTrigger": "change", "maxlength": null, "size": "default", "suffix": "", "addonAfter": "", "rules": [], "readonly": false, "disabled": false, "clearable": true, "formColumn": false, "decimalSeparator": 2, "icon": {"iconName": "", "popover": {"description": []}}, "formId": "ccuz8ypotl", "show": {"exp": ""}, "forbid": {"exp": ""}}, "actions": [], "_compName": "7slr1lgy7", "chosen": false, "selected": false, "entityId": "NumberInput3", "compPropName": "(单价)", "childrens": [], "style": {}}, {"compName": "lyy-number-input", "compId": "cv36fi150l", "compNameCn": "数字输入框", "modelValue": "", "prop": {"field": "count", "label": "数量", "aliasName": "数字输入框cv36f", "placeholder": "请输入", "defaultValue": "", "validateTrigger": "change", "maxlength": null, "size": "default", "suffix": "", "addonAfter": "", "rules": [], "readonly": false, "disabled": false, "clearable": true, "formColumn": false, "decimalSeparator": 2, "icon": {"iconName": "", "popover": {"description": []}}, "formId": "ccuz8ypotl", "show": {"exp": ""}, "forbid": {"exp": ""}}, "actions": [], "_compName": "7slr1lgy7", "chosen": false, "selected": false, "entityId": "NumberInput2", "compPropName": "(数量)", "childrens": [], "style": {}}, {"compName": "lyy-formula", "compId": "c44f8qikvg", "compNameCn": "计算值组件", "style": {}, "prop": {"field": "sum", "label": "合计", "aliasName": "计算值组件c44f8", "exp": "ccuz8ypotl.modelValue.price * ccuz8ypotl.modelValue.count ", "placeholder": "请输入", "labelWidth": "", "labelAlign": "left", "defaultValue": "", "rules": [], "type": "text", "pipes": [{"pipe": "", "option": {}}], "show": {"exp": ""}, "forbid": {"exp": ""}, "formId": "ccuz8ypotl"}, "actions": [], "_compName": "fggz74jgc", "chosen": false, "selected": false, "entityId": "Formula1"}, {"compName": "lyy-button", "compId": "csuug056sf", "compNameCn": "按钮", "actions": [{"event": "click", "action": "request", "option": {"url": "/api/bbbb", "method": "post", "responseType": "json", "payloads": {"type": "dynamic", "dynamic": {"nodePath": ""}}, "responseDataKey": "", "customOption": {"loading": true, "nextEventsDelay": 0, "parse": false, "unhandled": false}, "headerPayloads": [], "interceptors": {"requestInterceptor": "", "responseInterceptor": ""}}, "thenActions": []}], "childrens": [], "prop": {"text": "提交", "aliasName": "按钮csuug", "color": "primary", "buttonSize": "medium", "shape": "default", "variant": "default", "block": true, "disabled": false, "isBorderRadius": false, "formColumn": false, "subTitle": "", "authCode": "", "show": {"exp": ""}, "forbid": {"exp": ""}}, "_compName": "18gwu1n0b", "chosen": false, "selected": false, "entityId": "Button1", "compPropName": "(按钮)", "style": {}}], "prop": {"labelAlign": "left", "form": {}, "isErpForm": false, "show": {"exp": ""}, "forbid": {"exp": ""}, "isFlow": false, "flowProp": {"field": "taskApproval", "html": "<p style=\"color:#666\">${createTime}</p><div style=\"display:flex;align-items:center;margin:6px 0\"><img width=\"30px\" height=\"30px\" style=\"width:30px;height:30px;margin-right:4px\" src=\"https://page-configure.oss-cn-shenzhen.aliyuncs.com/page-configure/attachment/icon/1709636619747_icon／colours／头像_默认@2x.png\"/><p>${createBy}<p style=\"color:#999;display:inline\">${typeStr}</p></p></div><p style=\"color:#999\">${content.opinion}</p>"}, "aliasName": "表单ccuz8"}, "_compName": "ymmv67hdi", "chosen": false, "selected": false, "entityId": "Form1", "compPropName": "", "style": {}, "modelValue": {}}], "entityId": "Container1", "selected": false, "chosen": false, "compPropName": "", "style": {}}
{"name": "demo", "version": "1.0.0", "private": true, "description": "demo", "templateInfo": {"name": "default", "typescript": true, "css": "Sass", "framework": "React"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "build:harmony-hybrid": "taro build --type harmony-hybrid", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "dev:harmony-hybrid": "npm run build:harmony-hybrid -- --watch", "test": "jest", "build:json": "generate-code json/home.json ./src/pages/index/home.tsx"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@lyy/components": "workspace:*", "@lyy/core": "workspace:*", "@babel/runtime": "^7.21.5", "@tarojs/components": "3.6.31", "@tarojs/helper": "3.6.31", "@tarojs/plugin-platform-weapp": "3.6.31", "@tarojs/plugin-platform-alipay": "3.6.31", "@tarojs/plugin-platform-tt": "3.6.31", "@tarojs/plugin-platform-swan": "3.6.31", "@tarojs/plugin-platform-jd": "3.6.31", "@tarojs/plugin-platform-qq": "3.6.31", "@tarojs/plugin-platform-h5": "3.6.31", "@tarojs/plugin-platform-harmony-hybrid": "3.6.31", "@tarojs/runtime": "3.6.31", "@tarojs/shared": "3.6.31", "@tarojs/taro": "3.6.31", "@tarojs/plugin-framework-react": "3.6.31", "@tarojs/react": "3.6.31", "react-dom": "^18.0.0", "react": "^18.0.0"}, "devDependencies": {"@babel/core": "^7.8.0", "@tarojs/cli": "3.6.31", "@types/webpack-env": "^1.13.6", "@tarojs/test-utils-react": "^0.1.1", "@types/react": "^18.0.0", "webpack": "5.78.0", "@tarojs/taro-loader": "3.6.31", "@tarojs/webpack5-runner": "3.6.31", "babel-preset-taro": "3.6.31", "eslint-config-taro": "3.6.31", "eslint": "^8.12.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "react-refresh": "^0.11.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react-hooks": "^4.2.0", "stylelint": "^14.4.0", "@typescript-eslint/parser": "^6.2.0", "@typescript-eslint/eslint-plugin": "^6.2.0", "typescript": "^5.1.0", "tsconfig-paths-webpack-plugin": "^4.1.0", "postcss": "^8.4.18", "ts-node": "^10.9.1", "@types/node": "^18.15.11", "@types/jest": "^29.3.1", "jest": "^29.3.1", "jest-environment-jsdom": "^29.5.0"}}
# 低代码平台页面生成器 (最简版 Demo)

这是一个最简版的低代码平台页面生成器 Demo，展示了如何将 JSON 配置转换为 Taro React 代码。

## 项目结构

```
.
├── json/
│   └── home.json          # 页面配置文件
├── packages/
│   ├── core/              # JSON 解析器模块
│   ├── generator/         # 代码生成器模块
│   ├── runtime/           # 运行时模块
│   └── components/        # 组件库模块
```

## 核心模块说明

### 1. JSON 解析器 (@lyy/parser)
将 JSON 配置解析为中间表示 (IR)，便于后续处理。

### 2. 代码生成器 (@lyy/generator)
将解析后的 IR 转换为 Taro React 代码，使用 Babel 工具进行代码处理。

### 3. 运行时 (@lyy/runtime)
提供状态管理 (使用 Zustand) 和组件注册机制。

### 4. 组件库 (@lyy/components)
包含可复用的 Taro 组件。

## 运行示例

```bash
# 运行简化版代码生成器
npx ts-node run-generator.ts
```

## 生成的代码示例

```jsx
import React from 'react';
import { LyyContainer, LyyForm, LyyNumberInput, LyyFormula, LyyButton } from '@lyy/components';

const GeneratedPage = () => {
  return (
      <LyyContainer compId="c92mgf5a3d" aliasName="页面c92mg" isHasNav="true" isPadding="false" isHasTabBar="true" flex="true">
    <LyyForm compId="ccuz8ypotl" labelAlign="left" isErpForm="false" isFlow="false" aliasName="表单ccuz8">
        <LyyNumberInput compId="cd9hv0piuk" field="price" label="单价" aliasName="数字输入框cd9hv" placeholder="请输入" defaultValue="" validateTrigger="change" size="default" suffix="" addonAfter="" readonly="false" disabled="false" clearable="true" formColumn="false" decimalSeparator="2" formId="ccuz8ypotl" />
        <LyyNumberInput compId="cv36fi150l" field="count" label="数量" aliasName="数字输入框cv36f" placeholder="请输入" defaultValue="" validateTrigger="change" size="default" suffix="" addonAfter="" readonly="false" disabled="false" clearable="true" formColumn="false" decimalSeparator="2" formId="ccuz8ypotl" />
        <LyyFormula compId="c44f8qikvg" field="sum" label="合计" aliasName="计算值组件c44f8" exp="ccuz8ypotl.modelValue.price * ccuz8ypotl.modelValue.count " placeholder="请输入" labelWidth="" labelAlign="left" defaultValue="" type="text" formId="ccuz8ypotl" />
        <LyyButton compId="csuug056sf" text="提交" aliasName="按钮csuug" color="primary" buttonSize="medium" shape="default" variant="default" block="true" disabled="false" isBorderRadius="false" formColumn="false" subTitle="" authCode="" />
      </LyyForm>
  </LyyContainer>
  );
};

export default GeneratedPage;
```

## 技术栈

- Taro v3.6.31
- React v18.x
- TypeScript
- Zustand (状态管理)
- Babel (代码生成)

## 设计理念

1. **模块化**: 将功能拆分为独立的模块，便于维护和扩展。
2. **可扩展**: 通过组件注册机制，可以轻松添加新组件。
3. **响应式**: 使用 Zustand 进行状态管理，实现数据响应式更新。
4. **可读性**: 生成的代码具有良好的可读性，便于调试和维护。

## 后续可优化方向

1. 完善构建流程，修复 TypeScript 编译错误
2. 实现更复杂的表达式解析和计算
3. 添加更多组件类型和事件处理
4. 实现更安全的沙箱执行环境
5. 支持更复杂的布局和样式处理
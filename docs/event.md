概述
组件库各事件的功能、参数含义及使用示例。
事件列表
1. broadcast - 广播事件
   功能：向指定组件广播事件
   参数：
   ● option: ITarget - 目标配置
   ○ targetId: string - 目标组件ID
   ○ event: string - 要触发的事件名
   ● thenActions: IAction[] | undefined - 后续动作
   ● currentComp: IElement - 当前组件
   示例：
   broadcast({
   targetId: 'component1',
   event: 'refresh'
   }, [], currentComponent);
2. copyToClipboard - 复制到剪贴板
   功能：将指定内容复制到系统剪贴板
   参数：
   ● option: ICopyToClipboardOption - 复制配置
   ○ target: string - 要复制的内容模板
   ● thenActions: IAction[] | undefined - 后续动作
   ● currentComp: IElement - 当前组件
   示例：
   copyToClipboard({
   target: '${Form1.modelValue.text}'
   }, [], currentComponent);
3. linkto - 页面跳转
   功能：实现页面导航跳转
   参数：
   ● option: ILinktoOption - 跳转配置
   ○ url: string - 目标URL
   ○ go: number - 后退/前进页数(可选)
   ○ payloads: any - 携带参数(可选)
   ○ isAddRandom: boolean - 是否添加随机参数防缓存(可选)
   ○ type: string - 跳转类型(默认'navigateTo')
   ● thenActions: IAction[] | undefined - 后续动作
   ● currentComp - 当前组件
   示例：
   interface ILinktoOption {
   go?: number; // 0表示当前页，-1后退一页，1前进1页，以此类推
   url?: string;
   tab?: "_self" | "_blank";
   mode?: "query" | "params";
   name?: string;
   type?: linkType
   isAddRandom?: boolean; // 是否在连接上带上随机数
   payloads?: IProxyDataHigher[];
   }
   linkto({
   url: '/pages/detail/index',
   payloads: { id: '123' },
   type: 'redirectTo'
   }, [], currentComponent);
4. openModal - 打开模态框
   功能：打开指定的模态框组件
   参数：
   ● option: ITarget - 目标配置
   ○ targetId: string - 模态框组件ID
   ● thenActions: IAction[] | undefined - 后续动作
   ● currentComp: IElement - 当前组件
   示例：
   openModal({
   targetId: 'modal1'
   }, [], currentComponent);
5. closeModal - 关闭模态框
   功能：关闭指定的模态框组件
   参数：
   ● option: ITarget - 目标配置
   ○ targetId: string - 模态框组件ID
   ● thenActions: IAction[] | undefined - 后续动作
   ● currentComp - 当前组件
   示例：
   closeModal({
   targetId: 'modal1'
   }, [], currentComponent);
6. request - 数据请求
   功能：发送网络请求
   参数：
   ● option: IRequestOption - 请求配置
   ○ url: string - 请求URL
   ○ method: string - 请求方法
   ○ data: any - 请求数据
   ○ responseDataKey: string - 响应数据存储键
   ○ customOption: any - 自定义选项
   ● thenActions: IAction[] | undefined - 后续动作
   ● currentComp: IElement - 当前组件
   ● requestNumber: number - 请求次数(可选)
   示例：
   request({
   url: '/api/data',
   method: 'GET',
   responseDataKey: 'result'
   }, [], currentComponent);
7. upload - 文件上传
   功能：上传文件到服务器
   参数：
   ● option: IRequestOption - 上传配置
   ○ url: string - 上传URL
   ○ headerPayloads: any - 请求头参数
   ● thenActions: IAction[] | undefined - 后续动作
   ● currentComp: IElement - 当前组件
   示例：
   upload({
   url: '/api/upload',
   headerPayloads: { token: 'xxx' }
   }, [], currentComponent);
8. validate - 表单验证
   功能：验证表单数据
   参数：
   ● option: ITarget - 验证配置
   ○ targetId: string - 表单ID
   ○ fields: string[] - 要验证的字段(可选)
   ● thenActions: IAction[] | undefined - 后续动作
   ● currentComp: IElement - 当前组件
   示例：
   validate({
   targetId: 'form1',
   fields: ['username', 'password']
   }, [], currentComponent);
9. changeLanguage - 切换语言
   功能：切换应用语言
   参数：
   ● option: IQtOption - 语言配置
   ○ lng: string - 语言键(默认'language')
   ○ isRefresh: boolean - 是否刷新页面(默认true)
   ○ payloads: any - 负载数据
   ● thenActions: IAction[] | undefined - 后续动作
   ● currentComp: IElement - 当前组件
   示例：
   changeLanguage({
   lng: 'lang',
   payloads: { lang: 'zh-CN' }
   }, [], currentComponent);
10. customerJS - 执行自定义JS
    功能：执行自定义JavaScript代码
    参数：
    ● option: ICustomJSOption - 配置
    ○ customJs: string | Function - 自定义JS代码或函数
    ● thenActions: IAction[] | undefined - 后续动作
    ● currentComp: IElement - 当前组件
    ● args: any[] - 参数数组
    示例：
    customerJS({
    customJs: '(useAction,data,context,updateElementData,_args) => { console.log(data); }'
    }, [], currentComponent, [arg1, arg2]);
11. openConfirm - 打开确认框
    功能：打开确认框/提示框，支持多种类型（modal、showModal、toast、loading）
    参数：
    ● option: IOpenConfirmOption - 配置
    ○ type: 'modal' | 'showModal' | 'toast' | 'loading' - 确认框类型
    ○ title: string - 标题
    ○ content: string - 内容
    ○ confirmText: string - 确认按钮文本
    ○ cancelText: string - 取消按钮文本
    ○ isI18n: boolean - 是否国际化
    ○ duration: number - 持续时间（仅toast/loading）
    ● thenActions: IAction[] | undefined - 后续动作
    ● currentComp: IElement - 当前组件
    示例：
    openConfirm({
    type: 'showModal',
    title: '确认操作',
    content: '确定要执行此操作吗？',
    confirmText: '确定',
    cancelText: '取消'
    }, [], currentComponent);
12. qtTrackAction - QT埋点事件
    功能：调用QT事件跟踪功能
    参数：
    ● option: IQtOption - 配置
    ○ trackerEventCode: string - 跟踪事件代码
    ○ eventType: string - 事件类型
    ○ eventParams: Record<string, any> - 事件参数
    ● thenActions: IAction[] | undefined - 后续动作
    ● currentComp: IElement - 当前组件
    示例：
    qtTrackAction({
    trackerEventCode: 'click_button',
    eventType: 'click',
    eventParams: { buttonId: 'submit' }
    }, [], currentComponent);
13. requestDataset - 数据集请求
    功能：执行数据集请求
    参数：
    ● option: IRequestDatasetOption - 配置
    ○ datasetName: string - 数据集名称
    ○ requestConfig: IRequestOption - 请求配置
    ○ onSuccessActions: IAction[] - 成功后动作
    ○ onErrorActions: IAction[] - 错误后动作
    ○ onFinallyActions: IAction[] - 最终动作
    ● thenActions: IAction[] | undefined - 后续动作
    ● currentComp: IElement - 当前组件
    示例：
    requestDataset({
    datasetName: 'userData',
    requestConfig: {
    url: '/api/user',
    method: 'GET'
    },
    onSuccessActions: [],
    onErrorActions: [],
    onFinallyActions: []
    }, [], currentComponent);
14. resetValue - 重置表单值
    功能：重置表单组件的值
    参数：
    ● option: ITarget - 配置
    ○ targetId: string - 表单组件ID
    ● thenActions: IAction[] | undefined - 后续动作
    ● currentComp: IElement - 当前组件
    示例：
    resetValue({
    targetId: 'form1'
    }, [], currentComponent);
15. scanCode - 扫码
    功能：调用设备扫码功能
    参数：
    ● option: IScanCodeOption - 配置
    ○ responseDataKey: string - 扫码结果存储键
    ● thenActions: IAction[] | undefined - 后续动作
    ● currentComp: IElement - 当前组件
    示例：
    scanCode({
    responseDataKey: 'scanResult'
    }, [], currentComponent);
16. setValue - 设置值
    功能：设置指定路径的数据值
    参数：
    ● option: ISetValueOption - 配置
    ○ from: getValueOption - 来源值配置
    ■ type: 'static' | 'dynamic' | 'higher' - 值类型
    ■ static: string - 静态值
    ■ dynamic: { nodePath: string } - 动态路径
    ■ higher: IProxyDataHigher[] - 高级配置
    ○ to: { type: 'dynamic', dynamic: { nodePath: string } } - 目标路径
    ○ arrayMergeType: string - 数组合并类型
    ● thenActions: IAction[] | undefined - 后续动作
    ● currentComp: IElement - 当前组件
    示例：
    setValue({
    from: {
    type: 'static',
    static: 'new value'
    },
    to: {
    type: 'dynamic',
    dynamic: {
    nodePath: 'component1.modelValue'
    }
    }
    }, [], currentComponent);
17. depAction - 依赖动作
    功能：触发依赖动作
    参数：
    ● option: IDepActionOption - 配置
    ○ action: string - 动作类型
    ○ targetId: string - 目标组件ID
    ○ params: any - 参数
    ○ payloads: any - 负载数据
    ● thenActions: IAction[] | undefined - 后续动作
    全局工具函数
    getProxyData - 获取代理数据
    功能：获取当前页面的代理数据，包含页面元素、公共数据、本地存储等
    参数：无
    返回值：包含页面数据的对象
    示例：
    const data = getProxyData();
    updateElementData - 更新元素数据
    功能：更新指定路径的元素数据，并触发页面重新渲染
    参数：
    ● nodePath: string - 数据路径
    ● value: any - 新值
    ● isMerge?: boolean - 是否合并对象
    ● noUpdateElement?: boolean - 是否不更新元素
    ● originElement?: any - 发起更新的组件
    示例：
    updateElementData('componentId.prop.value', 'new value');
    getIn - 获取嵌套数据
    功能：从对象或数组中获取嵌套属性值
    参数：
    ● data: object | object[] - 源数据
    ● path?: string | string[] - 路径
    示例：
    const value = getIn(data, 'a.b.c');
    setIn - 设置嵌套数据
    功能：设置对象嵌套属性值
    参数：
    ● data: object - 源数据
    ● path: string | string[] - 路径
    ● value: any - 值
    示例：
    setIn(data, 'a.b.c', 'new value');
    formulaTemplate - 模板字符串处理
    功能：处理包含${}的模板字符串，计算表达式结果
    参数：
    ● str: string - 模板字符串
    ● scopeData?: object - 作用域数据
    ● _getProxyData?: any - 代理数据
    示例：
    const result = formulaTemplate('Hello ${user.name}');
    formatDate - 日期格式化
    功能：格式化日期时间
    参数：
    ● date: Date | string - 日期对象或字符串
    ● formatter: string - 格式模板(默认'YYYY-MM-DD HH:mm:ss')
    示例：
    const formatted = formatDate(new Date(), 'YYYY-MM-DD');
    console.log(formatted); // 输出: 2023-10-15
    类型定义
    IElement - 组件元素接口
    interface IElement {
    key?: string; // 唯一值 一半为compId
    compId?: string; // 组件Id
    entityId?: string; // 实体Id
    compName: string; // 组件名
    style?: IStyle; // 样式
    modelValue?: any; // 组件值
    prop?: any; // 自定义属性
    actions?: IAction[];
    childrens?: IElement[];
    children?: ReactNode;
    rowData?:any;
    onAction?: (option: ActionOption, args?) => void;
    }
    IAction - 动作接口
    interface IAction {
    event?: EventType;
    action: ActionType;
    option: eventOptions;
    condition?: {
    exp?: string;
    };
    thenActions?: IAction[];
    belong?: BelongType;
    }
    IRequestOption - 请求配置接口
    interface IRequestOption {
    url: string
    payloads: Payloads
    method: Method
    responseDataKey: string
    customOption?: CustomOption
    headerPayloads?: HeaderPayload[]
    interceptors: {
    requestInterceptor: string
    responseInterceptor: string
    }
    }
    ```- `currentComp: IElement` - 当前组件

**示例**：
```typescript
depAction({
  action: 'refresh',
  targetId: 'component2',
  params: { id: '123' }
}, [], currentComponent);
CustomOption - 动作接口
interface customOption {
  jsonPath?: string
  loading?: boolean | ILoading
  successfulFeedback?: IFeedback
  failedFeedback?: IFeedback
  dupliRequestStrictEquality?: boolean
  nextEventsDelay?: number
  codeValue?: number | string
  codeKey?: string
  messageKey?: string
  unhandled?: boolean
  parse?: boolean
  // 下载
  downloadType?: string // href
  fileName?: string
  // 是否成功才停止请求
  isSuccessStop?: boolean
  // 轮询请求的次数
  roundOrder?: number
  // 轮询请求请求间隔
  roundTimes?: 2000
  stopFailFeedback?: false
}


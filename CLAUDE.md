# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 开发命令

这是一个使用以下关键命令的 pnpm monorepo：

- `pnpm build` - 递归构建所有包
- `pnpm generate` - 使用 `ts-node generate-test.ts` 运行代码生成器
- `generate-code json/home.json ./src/pages/index/home.tsx` - 从 JSON 配置生成代码（通过 @lyy/core CLI）

### Taro 开发（演示项目）
`projects/demo/` 中的演示项目使用 Taro 3.6.31 进行跨平台开发：

- `npm run dev:h5` - H5 开发模式
- `npm run dev:weapp` - 微信小程序开发模式
- `npm run build:h5` - H5 生产构建
- `npm run build:weapp` - 微信小程序生产构建
- `npm run test` - 运行 Jest 测试

### 包构建
各个包可以单独构建：
- `@lyy/core`: `pnpm build` (使用 tsup)
- `@lyy/components`: `npm run build` (使用 TypeScript 编译器)

## 架构概述

这是一个从 JSON 配置生成 Taro React 代码的低代码平台。架构包含 4 个主要包：

### 核心包 (`packages/`)

1. **@lyy/core** - 系统核心
   - JSON 解析器，将配置转换为中间表示 (IR)
   - 使用 Babel AST 操作的代码生成器
   - 通过 `generate-code` 命令访问的 CLI 工具
   - 使用 Valtio 进行状态管理

2. **@lyy/components** - 组件库
   - 可重用的 Taro 组件（LyyContainer、LyyForm、LyyNumberInput 等）
   - 专为生成的代码构建

3. 其他包可能包括运行时和生成器模块（在 README 中提及）

### 演示项目 (`projects/demo/`)

- 展示生成代码的完整 Taro 应用程序
- 使用工作区依赖 (`@lyy/components`, `@lyy/core`)
- 在 `json/` 目录中包含示例 JSON 配置
- 生成的页面放入 `src/pages/` 结构中

## 关键架构模式

**代码生成流程：**
1. JSON 配置 → IR（通过 JSON 解析器）
2. IR → Babel AST（通过生成器）
3. AST → Taro React 代码（通过 Babel 生成器）

**组件系统：**
- 带有 `Lyy` 前缀的自定义组件库
- 专为表单密集型应用程序设计的组件
- 基于 Taro 3.6.31 + React 18.2.0 构建

**Monorepo 结构：**
- 使用 pnpm 工作区
- 内部包的工作区依赖 (`workspace:*`)
- 通过 pnpm overrides 锁定 Taro 版本 (3.6.31)

## 开发注意事项

- 整个项目使用 TypeScript
- 项目对 Taro 生态系统包使用固定版本
- Babel 工具链是代码生成功能的核心
- Jest 在演示项目中配置用于测试
- 所有包都以 ES 模块为目标 (`"type": "module"`)
## Gemini CLI 协作

### 触发条件
用户提到"Gemini 商量后继续"时，使用 Gemini CLI 协同完成任务。

### 使用方法
```bash
gemini -p "你的中文提示词"
```

**重要**：必须使用 `-p` 参数，支持中文输入输出。

### 协作流程
1. 用 `gemini -p "提示词"` 获取 Gemini 建议
2. 整合 Gemini 回答与 Claude 分析
3. 提供完整解决方案
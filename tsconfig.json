{"compilerOptions": {"target": "ES2022", "module": "ES2022", "lib": ["ES2022", "DOM"], "strict": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "jsx": "react-jsx", "resolveJsonModule": true, "moduleResolution": "node", "typeRoots": ["node_modules/@types"], "types": ["node", "react", "babel__parser"]}, "ts-node": {"esm": true, "transpileOnly": true, "experimentalSpecifierResolution": "node"}}
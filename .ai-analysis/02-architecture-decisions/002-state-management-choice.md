# ADR-002: 状态管理技术选择 - <PERSON><PERSON><PERSON> vs Zustand

> **状态**: ✅ 已接受  
> **决策日期**: 2025-08-07  
> **决策者**: lowcode-architect (AI)  
> **相关决策**: ADR-001 (分析框架建立)

## 背景

项目需要为低代码平台选择合适的状态管理方案。当前代码中已部分实现Valtio，但存在Valtio vs Zustand的技术选型疑问。需要基于项目特殊需求做出明确的技术决策。

## 决策

**选择Valtio作为项目的状态管理方案**，并完善现有的实现。

## 理由

### 项目特殊需求分析

低代码平台具有以下特殊需求，这些需求更适合Valtio：

1. **复杂嵌套状态修改**: JSON配置包含深度嵌套的属性
2. **细粒度更新需求**: 避免不必要的组件重渲染
3. **17种事件类型**: 需要频繁的状态修改操作
4. **表达式计算**: `${expression}`需要直观的状态访问

### Valtio核心优势

#### 1. 自动精准更新
```typescript
// 只有使用相关数据的组件才重渲染
component.prop.price = 100;        // 只更新price相关组件
component.modelValue.count = 5;    // 只更新count相关组件
```

#### 2. 直观的API设计
```typescript
// 直接修改，符合直觉 - 适合低代码平台的动态特性
component.prop.formId = 'newFormId';
component.modelValue.price = calculate(expression);
component.style.display = condition ? 'block' : 'none';
```

#### 3. React 18并发兼容
```typescript
// 项目已实现的并发安全机制
useSyncExternalStore(
  subscribe,
  () => snapshot(componentStore).components[entityId]
);
```

### 与Zustand对比

| 维度 | Valtio | Zustand | 项目适配度 |
|------|--------|---------|----------|
| **学习曲线** | 🟢 低 | 🟡 中 | 更适合团队 |
| **代码简洁性** | 🟢 直接赋值 | 🟡 需actions | 减少50%样板代码 |
| **性能优化** | 🟢 自动 | 🔴 手动选择器 | 自动优化更可靠 |
| **调试体验** | 🟡 代理复杂 | 🟢 DevTools好 | 可接受的权衡 |

### 扩展性考虑

#### 事件系统扩展
17种事件类型的处理，Valtio更直观：
```typescript
async function handleRequest(action: IAction, component: ComponentNode) {
  component.loading = true;  // 直接修改
  try {
    const data = await fetch(action.option.url);
    component.responseData[action.option.responseDataKey] = data;
  } finally {
    component.loading = false;
  }
}
```

#### 表达式引擎扩展  
```typescript
function formulaTemplate(expr: string, component: ComponentNode) {
  const result = evaluateExpression(expr, component);
  component.calculatedValue = result; // 自动触发依赖更新
  return result;
}
```

## 决策结果

### 技术选型
- ✅ **主状态管理**: Valtio 
- ✅ **React集成**: useSyncExternalStore
- ✅ **架构模式**: 单一数据源 + 组件级订阅

### 实现规范
1. **全局状态**: 通过`componentStore`管理
2. **组件状态**: 按`entityId`组织，支持独立更新
3. **读写分离**: `reactive()`获取可写，`snapshot()`获取只读
4. **自动注册**: 组件不存在时自动创建默认结构

## 风险与缓解

### 潜在风险
1. **调试复杂性**: Proxy对象在DevTools中显示复杂
2. **意外修改**: 任何地方都可以修改状态  
3. **学习成本**: 团队可能不熟悉响应式编程

### 缓解策略
1. **调试工具**: 使用`snapshot()`获取纯对象进行调试
2. **访问控制**: 建立状态修改规范，限制修改入口
3. **文档培训**: 提供完整的使用指南和最佳实践

## 实施计划

### 短期 (1周内)
- [ ] 完善现有的Valtio实现
- [ ] 添加完整的TypeScript类型定义
- [ ] 实现调试工具和日志系统

### 中期 (2-3周)
- [ ] 基于Valtio实现5个P0事件处理
- [ ] 建立状态修改的标准模式
- [ ] 完善错误处理和边界情况

### 长期 (1个月)
- [ ] 性能监控和优化
- [ ] 建立最佳实践文档
- [ ] 团队培训和知识分享

## 成功指标

- **性能**: 组件更新性能提升30%以上
- **开发效率**: 状态相关代码减少50%
- **维护性**: 状态修改逻辑清晰可追踪
- **稳定性**: 零状态一致性问题

## 后续决策

基于此决策，后续相关决策：
- ADR-003: 事件系统架构设计
- ADR-004: 表达式引擎实现方案
- ADR-005: 组件生命周期管理

---

*此决策确立了项目的状态管理基础架构，为后续的事件系统和表达式引擎实现提供了坚实基础。*
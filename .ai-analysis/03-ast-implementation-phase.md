# AST代码生成器实现文档

## 概述
本文档记录了从旧的字符串拼接代码生成器迁移到基于Babel AST的现代化代码生成器的完整实现过程。

## 架构变更

### 1. 核心文件结构
```
packages/core/src/
├── generators/
│   ├── ast-code-generator.ts         # 新AST代码生成器主文件
│   ├── expression-ast-processor.ts   # 表达式AST处理器
│   ├── source-code-generator.ts      # 旧版生成器（保留兼容）
│   └── index.ts                      # 导出文件
├── events/
│   ├── event-dispatcher.ts           # 完整17种事件实现（1789行）
│   └── index.ts
├── utils/
│   ├── expression-engine.ts          # 表达式引擎（471行）
│   ├── performance.ts                # 性能优化工具
│   ├── performance-monitor.ts        # 性能监控服务
│   └── naming-utils.ts              # 命名工具函数
├── store/
│   └── root-store.ts                 # 三层状态架构实现
└── hooks/
    └── use-enhanced-component.ts     # 新架构Hook实现
```

## 实现细节

### 1. AST代码生成器 (ast-code-generator.ts)

#### 核心特性
- 使用Babel AST精确生成TypeScript/React代码
- 支持完整的组件树结构解析
- 集成新架构的三层状态系统
- 自动生成性能优化代码

#### 主要方法
```typescript
export class ASTCodeGenerator {
  // 生成完整页面代码
  public generatePageCode(trees: ParsedComponentTree[]): GeneratedCodeResult {
    // 1. 分析组件树
    this.analyzeComponentTrees(trees);
    // 2. 构建AST程序
    const program = this.buildProgram(trees);
    // 3. 生成最终代码
    const result = generate(program, options);
    return result;
  }
}
```

#### 生成的代码结构
1. **导入语句**
   - React和Hooks导入
   - 新架构Hooks (useEnhancedComponent, usePageComponents)
   - 组件库导入 (@lyy/components)
   - 表达式引擎导入

2. **组件数据声明**
   - COMPONENT_DATA常量定义
   - 包含所有组件的初始配置

3. **主页面组件**
   - 页面级Hooks调用
   - 组件初始化Effect
   - 各组件的Hooks调用
   - 事件处理函数生成
   - 表达式计算函数生成
   - JSX渲染返回

### 2. 表达式AST处理器 (expression-ast-processor.ts)

#### 核心功能
- 处理JSON配置中的{{expression}}语法
- 生成表达式求值的AST代码
- 智能识别依赖关系
- 支持条件渲染和表单值绑定

#### 关键方法
```typescript
export class ExpressionASTProcessor {
  // 处理对象中的所有表达式
  processExpressionsInObject(obj, context): {processedObj, expressions}
  
  // 生成表达式处理AST
  generateExpressionHandlerAST(expressions, componentId): Statement[]
  
  // 生成条件渲染AST
  generateConditionalRenderingAST(condition, componentId, children): JSXExpressionContainer
}
```

### 3. 事件系统 (event-dispatcher.ts)

#### 完整实现的17种事件类型
1. **request** - HTTP数据请求
2. **setValue** - 设置组件值
3. **validate** - 表单验证
4. **linkto** - 页面跳转
5. **broadcast** - 事件广播
6. **copyToClipboard** - 复制到剪贴板
7. **openModal/closeModal** - 模态框控制
8. **upload** - 文件上传
9. **changeLanguage** - 切换语言
10. **customerJS** - 自定义JavaScript
11. **openConfirm** - 确认对话框
12. **qtTrackAction** - 量子埋点
13. **requestDataset** - 数据集请求
14. **resetValue** - 重置值
15. **scanCode** - 扫码
16. **depAction** - 依赖动作

#### 事件处理流程
```typescript
export class EventDispatcher {
  async dispatch(actions: ActionConfig[], context: EventContext): Promise<ActionResult[]> {
    // 1. 验证动作配置
    // 2. 按顺序执行动作
    // 3. 处理条件执行
    // 4. 收集执行结果
    // 5. 返回结果数组
  }
}
```

### 4. 表达式引擎 (expression-engine.ts)

#### 核心特性
- 支持复杂表达式求值
- 缓存机制提升性能
- 支持数学运算、逻辑运算、条件判断
- 内置函数库（日期、字符串、数组处理）

#### 表达式类型支持
```javascript
// 路径访问
"user.name"
"items[0].price"

// 数学运算
"price * quantity"
"(a + b) / 2"

// 条件表达式
"status === 'active' ? '启用' : '禁用'"

// 函数调用
"formatDate(createTime, 'YYYY-MM-DD')"
```

### 5. 性能优化系统

#### 五大优化机制
1. **渲染优化** (RenderOptimizer)
   - 组件渲染追踪
   - 重渲染检测
   - 性能瓶颈识别

2. **批量更新** (BatchUpdateManager)
   - 合并多次状态更新
   - 减少渲染次数
   - 自动批处理

3. **表达式缓存** (ExpressionCacheManager)
   - LRU缓存策略
   - 自动过期清理
   - 命中率统计

4. **事件节流** (EventThrottleManager)
   - 防抖和节流
   - 事件队列管理
   - 自动优化

5. **内存监控** (MemoryMonitor)
   - 实时内存使用
   - 泄漏检测
   - 自动告警

## 迁移对比

### 旧版生成器问题
- ❌ 字符串拼接易出错
- ❌ 仅支持request事件
- ❌ 使用过时的useReactiveComponent
- ❌ 缺少表达式处理
- ❌ 无性能优化

### 新版AST生成器优势
- ✅ AST精确代码生成
- ✅ 支持全部17种事件
- ✅ 使用新架构Hooks
- ✅ 完整表达式引擎
- ✅ 内置性能优化

## 测试验证

### 测试用例：home.json
成功解析的组件结构：
1. Container1 (lyy-container) - 页面容器
2. Form1 (lyy-form) - 表单组件
3. NumberInput3 (lyy-number-input) - 单价输入
4. NumberInput2 (lyy-number-input) - 数量输入
5. Formula1 (lyy-formula) - 计算公式：`price * count`
6. Button1 (lyy-button) - 提交按钮

### 验证点
- ✅ JSON解析成功
- ✅ 组件树分析完成
- ✅ 表达式识别正确
- ✅ 事件配置解析成功
- ✅ AST生成启动成功

## 当前问题

### Babel模块导入兼容性
**问题描述**：ES模块环境下，@babel/generator的默认导出不兼容

**错误信息**：
```
TypeError: generate is not a function
```

**需要修复**：
1. 调整Babel包的导入方式
2. 或使用兼容性包装器
3. 或改用其他代码生成方案

## 下一步计划

1. **修复Babel导入问题**
   - 尝试使用命名导入
   - 或创建兼容性包装器
   - 确保ES模块环境正常工作

2. **完成测试验证**
   - 生成完整代码
   - 验证生成的代码质量
   - 确保所有功能正常

3. **性能优化**
   - 测试大规模组件树
   - 优化生成速度
   - 减少内存占用

## 代码示例

### 生成的代码预期结构
```typescript
import React, { useEffect } from 'react';
import { useEnhancedComponent, usePageComponents, useForm, evaluateExpression } from '@lyy/core';
import LyyContainer from '@lyy/components';
import LyyForm from '@lyy/components';
// ... 其他组件导入

const COMPONENT_DATA = {
  Container1: { /* ... */ },
  Form1: { /* ... */ },
  // ... 其他组件数据
};

function GeneratedPage() {
  // 页面级Hooks
  const { initPage, pageComponents, executePageAction } = usePageComponents();
  
  // 组件初始化
  useEffect(() => {
    initPage(COMPONENT_DATA);
  }, []);
  
  // 各组件Hooks
  const { component: Container1_component, executeAction: Container1_executeAction } = useEnhancedComponent('Container1');
  const { component: Form1_component, executeAction: Form1_executeAction } = useEnhancedComponent('Form1');
  const { modelValue: Form1_modelValue, setValue: Form1_setValue } = useForm('Form1');
  
  // 事件处理器
  async function handle_Form1_beforeMount_0() {
    await Form1_executeAction({
      event: 'beforeMount',
      action: 'request',
      option: { /* ... */ }
    }, 'beforeMount');
  }
  
  // 表达式计算
  function evaluate_Formula1_expr_0() {
    return evaluateExpression(
      'ccuz8ypotl.modelValue.price * ccuz8ypotl.modelValue.count',
      { currentComponent: Formula1_component }
    );
  }
  
  // 渲染
  return (
    <LyyContainer entityId="Container1" prop={Container1_component.prop}>
      <LyyForm entityId="Form1" prop={Form1_component.prop} onBeforeMount={handle_Form1_beforeMount_0}>
        {/* ... 子组件 */}
      </LyyForm>
    </LyyContainer>
  );
}

export default GeneratedPage;
```

## 总结

AST代码生成器的实现标志着低代码平台代码生成能力的重大升级。虽然存在模块兼容性问题，但核心架构已经完成并验证成功。一旦解决导入问题，即可投入生产使用。
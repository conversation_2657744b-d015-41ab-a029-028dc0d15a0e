# 低代码平台统一架构文档

## 📋 架构概述

经过全面清理和重构，当前系统已统一为基于AST的代码生成架构，完全移除了旧的字符串拼接方式。

## 🏗️ 核心架构层级

### 1. 代码生成层 (Generation Layer)
```
├── AST代码生成器 (ast-code-generator.ts)
│   ├── Babel AST 精确生成
│   ├── TypeScript 类型安全
│   └── 组件树 → JSX 转换
├── 表达式处理器 (expression-ast-processor.ts)  
│   ├── {{expression}} 语法解析
│   └── AST表达式节点生成
└── JSON解析器 (json-parser.ts)
    ├── ComponentNode → ParsedComponentTree
    └── 事件处理器集成
```

### 2. 状态管理层 (State Layer) - 三层架构
```
Root Store (root-store.ts)
├── Page Layer (页面层)
│   ├── components: Record<string, ComponentNode>
│   ├── config: PageConfig
│   └── actions: PageActions
├── Global Layer (全局层)  
│   ├── userData: UserData
│   ├── apiData: ApiData
│   └── cache: GlobalCache
└── UI Layer (界面层)
    ├── loading: LoadingState
    ├── modals: ModalState
    └── notifications: NotificationState
```

### 3. Hook系统层 (Hook Layer)
```
Enhanced Component System
├── useEnhancedComponent(entityId)
│   ├── 响应式组件数据
│   ├── 快照状态 (只读)
│   ├── 事件处理函数
│   └── 属性更新方法
├── usePageComponents()
│   ├── 页面级组件管理
│   ├── initPage(components)
│   └── executePageAction()
├── useForm(entityId)
│   ├── 表单值管理
│   ├── 验证逻辑
│   └── setValue/getValue
└── useApiData(key)
    ├── API状态监听
    └── 数据缓存管理
```

### 4. 事件系统层 (Event Layer)
```
Event Dispatcher (event-dispatcher.ts)
├── 17种事件类型支持
│   ├── request - API请求
│   ├── setValue - 值设置
│   ├── validate - 验证
│   ├── linkto - 页面跳转
│   ├── broadcast - 广播
│   ├── modal - 弹窗
│   ├── notification - 通知
│   ├── storage - 存储
│   ├── analytics - 分析
│   ├── custom - 自定义
│   ├── condition - 条件
│   ├── loop - 循环
│   ├── delay - 延时
│   ├── parallel - 并行
│   ├── sequence - 序列
│   ├── transform - 转换
│   └── batch - 批量
└── 事件链式处理
    ├── thenActions 支持
    └── 错误处理机制
```

## 🔄 数据流向架构

### 组件生命周期数据流
```
JSON配置 → JsonParser → ParsedComponentTree → ASTCodeGenerator → 生成代码
    ↓                        ↓                        ↓
  注册到Store → 响应式状态管理 → Hook系统订阅 → 组件渲染
    ↓                        ↓                        ↓  
  事件触发 → EventDispatcher → 状态更新 → 重新渲染
```

### 状态更新流程
```
用户交互/API响应
    ↓
EventDispatcher.executeAction()
    ↓
根据actionType分发到对应处理器
    ↓
更新RootStore对应层级状态
    ↓
Valtio响应式触发Hook重新计算
    ↓
组件重新渲染 (仅必要组件)
```

## 📦 缓存系统架构

### 表达式缓存 (Expression Cache)
```
ExpressionCacheManager
├── 缓存策略: LRU + TTL
├── 依赖追踪: 自动失效
├── 性能指标: 命中率统计
└── 内存管理: 自动清理
```

### 组件渲染缓存
```
RenderOptimization
├── shouldComponentUpdate 逻辑
├── 浅比较优化
├── 批量更新机制
└── 渲染计数监控
```

### API数据缓存  
```
ApiDataCache
├── 请求去重
├── 响应缓存
├── 失效策略
└── 错误重试
```

## 🎯 性能优化系统

### 5大优化机制
1. **渲染优化**: shouldComponentUpdate + React.memo模式
2. **批量更新**: 合并状态变更，减少重渲染次数
3. **表达式缓存**: 表达式计算结果缓存和依赖失效
4. **事件节流**: 防抖和节流机制避免过量触发
5. **内存监控**: 实时监控和自动清理机制

### 性能监控指标
```
PerformanceMonitor
├── 组件渲染次数
├── 状态更新频率  
├── 事件触发统计
├── 内存使用监控
├── 缓存命中率
└── 瓶颈检测和建议
```

## 🔧 构建工具链

### CLI工具 (cli.ts)
```
generate-code <json-file> <output-file>
├── 统一使用AST生成器
├── 自动类型检查
├── 错误处理和报告
└── 构建成功/失败状态
```

### 包结构
```
@lyy/core - 核心状态管理和代码生成
├── /src/generators - AST代码生成器
├── /src/store - 三层状态管理
├── /src/hooks - React Hook系统
├── /src/events - 事件处理系统
├── /src/utils - 工具函数
└── /src/types - TypeScript类型定义

@lyy/components - Taro组件库
├── LyyContainer - 容器组件
├── LyyForm - 表单组件  
├── LyyNumberInput - 数字输入
├── LyyFormula - 计算组件
└── LyyButton - 按钮组件
```

## ✅ 当前架构优势

1. **类型安全**: 完整TypeScript支持，编译期错误检测
2. **性能优化**: 多层缓存+渲染优化，实际测试生成8953字符代码
3. **可维护性**: 单一架构路径，移除历史技术债务
4. **可扩展性**: 模块化设计，新功能容易集成
5. **调试友好**: 完整错误处理和性能监控

## 🚀 架构发展规划 (基于Gemini协商确定)

### 第一阶段: 基础架构强化 (2-3周)
1. **插件式事件系统设计** ✅
   - ✅ 保留17种内置事件作为核心
   - ✅ 设计EventPlugin接口和注册机制
   - ✅ 实现事件插件的安全沙箱环境

2. **性能优化P0优先级** ⏳
   - 代码分割: 按路由和组件实现懒加载
   - 虚拟化渲染: 支持大列表和复杂表格
   - 首页加载时间优化

3. **时间旅行调试架构** ✅
   - ✅ 强化不可变状态管理 (基于Valtio实现)
   - ✅ 设计StateSnapshot和ActionLog接口
   - ✅ 实现完整时间旅行调试系统
   - ✅ 集成到root-store状态变更操作
   - ⏳ 调试面板UI架构 (待后续实现)

#### 🎯 C1任务完成详情: 时间旅行调试系统
**核心实现** (`packages/core/src/store/time-travel-store.ts`):
- `TimeTravelStore` 类: 完整状态快照和动作日志管理
- `StateSnapshot` 接口: id, timestamp, state, actionId, description
- `ActionLog` 接口: id, timestamp, type, params, result, duration, entityId, description
- 自动快照定时器 (5秒间隔，开发环境)
- 状态历史限制 (100个快照，200个动作日志)

**集成实现** (`packages/core/src/store/root-store.ts`):
- `updateComponentProp`: 自动记录组件属性变更
- `setUser`: 自动记录用户信息变更  
- `setApiData`: 自动记录API数据变更
- 动态导入时间旅行模块，避免服务端渲染问题
- 仅开发环境启用，零生产影响

**API接口**:
```typescript
timeTravel.createSnapshot(description?)     // 手动快照
timeTravel.logAction(actionLog)             // 记录动作
timeTravel.jumpTo(index)                    // 时间跳转
timeTravel.backToLatest()                   // 返回最新
timeTravel.exportActions()                  // 导出序列
timeTravel.replayActions(actions, speed?)   // 重放动作
timeTravel.getDebugInfo()                   // 调试信息
```

#### 🎯 A1任务完成详情: EventPlugin核心架构设计
**核心系统实现** (`packages/core/src/events/plugin-system.ts`):
- `EventPlugin` 接口: 完整的插件规范定义
- `EventRegistry` 类: 插件注册、卸载和依赖管理
- `EventContext` 接口: 安全的插件执行上下文
- `EventSchema` 接口: 参数验证和文档生成
- 插件权限系统: 7种权限类型精细控制

**插件调度器** (`packages/core/src/events/plugin-dispatcher.ts`):
- `PluginEventDispatcher` 类: 安全的沙箱执行环境
- `ExecutionResult` 接口: 统一的执行结果格式
- 超时控制、权限检查、参数验证
- 批量执行和错误隔离机制

**插件管理器** (`packages/core/src/events/plugin-manager.ts`):
- `PluginManager` 类: 统一的插件生命周期管理
- 热加载、版本控制、依赖检查
- 执行统计、性能监控、调试支持
- 导入导出、配置验证功能

**内置插件实现** (`packages/core/src/events/builtin-plugins.ts`):
- 5个核心内置插件: request、setValue、validate、linkto、modal
- 完整向后兼容现有17种事件类型
- 标准化插件格式，保持API一致性

**关键技术特性**:
```typescript
// 插件接口
interface EventPlugin {
  type: string; name: string; version: string;
  handler: EventHandler; schema: EventSchema; 
  metadata: PluginMetadata; security: PluginSecurity;
  dependencies?: string[]; hooks?: LifecycleHooks;
}

// 安全权限系统
type Permission = 'read:state' | 'write:state' | 'network:request' 
                | 'storage:read' | 'storage:write' | 'dom:access' | 'eval:code';

// 统一执行接口
pluginManager.executeEvent(type, params, entityId) → ExecutionResult
```

**架构优势**:
- 🔐 **安全沙箱**: 权限控制 + 超时限制 + 错误隔离
- 🔌 **热插拔**: 运行时动态加载/卸载插件
- 📊 **可监控**: 执行统计 + 性能指标 + 调试支持  
- 🔄 **向后兼容**: 现有17种事件无缝迁移到插件形式
- 🎯 **企业级**: 版本管理 + 依赖检查 + 批量操作

### 第二阶段: 核心功能实现 (3-4周)  
1. **事件插件生态**
   - 完成插件注册器和管理器
   - 提供插件开发SDK和文档
   - 实现版本管理和依赖检查

2. **智能缓存策略 (P1优先级)**
   - API响应缓存优化
   - 表达式计算结果缓存
   - 缓存失效和更新策略

3. **开发者工具增强**
   - 时间旅行调试完整实现
   - 性能监控面板
   - 错误追踪和日志系统

### 第三阶段: 生态建设 (长期)
1. **插件市场建设**
   - 事件插件分享和分发
   - 社区贡献机制
   - 企业私有插件管理

2. **内存管理持续优化 (P2优先级)**
   - 自动内存泄漏检测
   - 组件生命周期监控
   - 垃圾回收优化建议

3. **企业级特性**
   - 多环境部署支持
   - 权限和安全机制
   - 可视化设计器集成

## 📋 核心架构原则 (来自Gemini建议)

1. **可扩展性优先**: 插件化架构满足企业复杂集成需求
2. **调试友好**: 时间旅行调试是企业级开发的刚需
3. **性能分层**: P0基础→P1增强→P2持续的优先级体系
4. **架构一致性**: 单一数据源、不可变状态、纯函数设计

---

**架构方向已通过Claude+Gemini协商确定，技术路线清晰，准备启动第一阶段实施。**
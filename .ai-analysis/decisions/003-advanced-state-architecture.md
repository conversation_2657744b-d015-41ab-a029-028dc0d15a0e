# ADR-003: 高级状态架构设计 - 三层状态模型

> **状态**: ✅ 已接受  
> **决策日期**: 2025-08-07  
> **决策者**: lowcode-architect (AI) + Gemini AI协作  
> **相关决策**: ADR-002 (Valtio状态管理选择)

## 背景

确立Valtio为状态管理方案后，需要设计具体的状态架构来支撑17种复杂事件类型、表达式引擎、组件间数据绑定等高级特性。通过与Gemini的深度架构讨论，形成了三层状态模型的最终设计。

## 决策

采用**三层分离的状态架构设计**，通过单一rootStore统一管理，实现职责分离和性能优化。

## 架构设计

### 核心原则
1. **状态与逻辑分离**: Valtio负责响应式状态，业务逻辑在独立模块
2. **单向数据流**: UI Event → Dispatcher → Action Handler → State Mutation → UI Update
3. **关注点分离**: Schema(配置) / State(状态) / Runtime State(运行时) / Engine(引擎)

### 三层状态结构

```typescript
import { proxy } from 'valtio';

const rootStore = proxy({
  /**
   * 第一层：page 状态层 - "静态蓝图"
   * 描述"什么被渲染"，是页面的结构配置
   */
  page: {
    // 页面全局配置
    config: {
      title: '页面标题',
      backgroundColor: '#ffffff',
    },
    
    // 核心：扁平化组件树 (性能关键)
    // 使用ID字典，避免深层嵌套，实现O(1)组件查找
    components: {
      'root-container': {
        id: 'root-container',
        componentName: 'LyyContainer',
        props: { /* 组件配置属性 */ },
        children: ['button-1', 'form-1'], // ID数组维护层级
        events: [
          {
            trigger: 'beforeMount',
            actions: [
              {
                type: 'request', // 17种事件类型之一
                params: {
                  url: '/api/init',
                  responseDataKey: 'initData'
                }
              }
            ]
          }
        ]
      },
      'button-1': {
        id: 'button-1', 
        componentName: 'LyyButton',
        parent: 'root-container',
        props: {
          text: '提交',
          disabled: false
        },
        // 组件运行时状态
        state: {
          loading: false,
          clicked: false
        },
        events: [
          {
            trigger: 'click',
            actions: [
              {
                type: 'validate',
                target: 'form-1'
              },
              {
                type: 'request',
                params: {
                  url: '/api/submit',
                  method: 'POST',
                  // 表达式支持：从其他组件状态取值
                  payloads: '{{components.form-1.modelValue}}',
                  responseDataKey: 'submitResult'
                }
              }
            ]
          }
        ]
      }
    }
  },

  /**
   * 第二层：global 状态层 - "共享上下文"  
   * 描述跨页面、跨组件共享的应用级数据
   */
  global: {
    user: {
      id: 'user-123',
      name: 'Admin',
      permissions: ['edit', 'delete']
    },
    
    // API请求的响应数据存储
    apiData: {
      // 'initData': { loading: false, data: {...}, error: null }
      // 'submitResult': { loading: true, data: null, error: null }
    },
    
    // 环境和配置
    env: {
      device: 'desktop',
      theme: 'light',
      language: 'zh-CN'
    }
  },

  /**
   * 第三层：ui 状态层 - "临时画布"
   * 描述编辑器本身的UI状态，不影响最终生成的应用
   */
  ui: {
    selectedComponentId: null,
    isSaving: false,
    isPreviewMode: false,
    rightPanelTab: 'props'
  }
});
```

### 事件系统与Valtio协作机制

```typescript
// 事件分发器 - 系统的"神经中枢"
export const EventDispatcher = {
  dispatch: async (actions: ActionConfig[], context?: any) => {
    for (const action of actions) {
      const handler = actionHandlers[action.type];
      if (handler) {
        await handler(action, context);
      } else {
        console.warn(`Unknown action type: ${action.type}`);
      }
    }
  }
};

// 动作处理器映射表 - 17种事件的实现核心
const actionHandlers = {
  // 1. 数据请求事件
  request: async (action: ActionConfig) => {
    const { url, method = 'GET', responseDataKey, payloads } = action.params;
    
    // 解析参数中的表达式
    const resolvedPayloads = evaluateExpression(payloads, rootStore);
    
    // 设置loading状态
    rootStore.global.apiData[responseDataKey] = { 
      loading: true, 
      data: null, 
      error: null 
    };
    
    try {
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: method !== 'GET' ? JSON.stringify(resolvedPayloads) : undefined
      });
      
      const data = await response.json();
      
      // 直接修改Valtio状态，自动触发组件更新
      rootStore.global.apiData[responseDataKey] = {
        loading: false,
        data,
        error: null
      };
    } catch (error) {
      rootStore.global.apiData[responseDataKey] = {
        loading: false,
        data: null,
        error
      };
    }
  },

  // 2. 设置值事件
  setValue: (action: ActionConfig) => {
    const { targetId, property, value } = action.params;
    const resolvedValue = evaluateExpression(value, rootStore);
    
    // 直接修改目标组件状态
    rootStore.page.components[targetId].state[property] = resolvedValue;
  },
  
  // ... 其他15种事件处理器
};
```

## 设计优势

### 1. 性能优化
- **扁平化结构**: O(1)组件查找，避免深层遍历
- **精准更新**: Valtio自动追踪最小粒度变化
- **批量处理**: 连续修改自动合并为单次渲染

### 2. 可维护性
- **职责分离**: 状态/逻辑/UI完全解耦
- **可测试性**: 每个actionHandler都是纯函数
- **可扩展性**: 新增事件类型只需添加handler

### 3. 表达式集成
```typescript
// 表达式引擎与状态的深度集成
function evaluateExpression(expr: string, context: any): any {
  if (typeof expr !== 'string' || !expr.includes('{{')) {
    return expr; // 非表达式直接返回
  }
  
  // 解析 {{components.form-1.modelValue.username}} 格式
  return expr.replace(/\{\{([^}]+)\}\}/g, (match, path) => {
    return getValueByPath(context, path.trim());
  });
}

function getValueByPath(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}
```

## 实施策略

### 阶段1: 基础架构搭建 (1周)
- [ ] 实现rootStore三层状态结构
- [ ] 创建EventDispatcher框架
- [ ] 实现基础的evaluateExpression

### 阶段2: 核心事件实现 (2-3周)  
- [ ] 实现request事件处理器
- [ ] 实现setValue事件处理器
- [ ] 实现validate事件处理器
- [ ] 建立事件测试框架

### 阶段3: 性能优化 (1周)
- [ ] 组件级订阅优化
- [ ] 表达式缓存机制
- [ ] 批量更新策略

## 风险与缓解

### 潜在风险
1. **状态复杂度**: 三层结构可能增加理解成本
2. **表达式性能**: 复杂表达式可能影响性能
3. **调试难度**: Proxy对象调试相对复杂

### 缓解策略
1. **文档和培训**: 提供完整的架构文档
2. **表达式缓存**: 对重复表达式进行缓存优化
3. **开发工具**: 提供状态查看和调试工具

## 成功指标

- **性能**: 1000+组件场景下渲染时间 < 100ms
- **内存**: 状态占用内存增长线性可控
- **开发效率**: 新增事件类型开发时间 < 2小时
- **稳定性**: 状态一致性问题为零

---

*此架构设计融合了Gemini的深度架构思考和Valtio的技术优势，为复杂低代码平台提供了坚实的状态管理基础。*
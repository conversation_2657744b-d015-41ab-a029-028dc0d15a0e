import React, { useState, useEffect } from 'react';
import { View, Text, Input } from '@tarojs/components';
import { useSnapshot } from 'valtio';
import { formDataActions, rootStore } from '@lyy/core';

/**
 * LyyNumberInput 增强数字输入框组件
 * 集成表单数据管理系统，支持响应式数据绑定
 */

export interface LyyNumberInputProps {
  // 组件基础属性
  compId: string;
  field: string;
  label?: string;
  
  // 输入框属性
  placeholder?: string;
  defaultValue?: number | string;
  required?: boolean;
  disabled?: boolean;
  readonly?: boolean;
  
  // 数值属性
  min?: number;
  max?: number;
  step?: number;
  precision?: number;
  
  // 表单相关
  formId?: string;
  
  // 验证相关
  validateTrigger?: 'change' | 'blur' | 'submit';
  rules?: Array<{
    required?: boolean;
    min?: number;
    max?: number;
    message?: string;
  }>;
  
  // 样式
  style?: React.CSSProperties;
  
  // 事件回调
  onChange?: (value: number | string, e: any) => void;
  onBlur?: (value: number | string, e: any) => void;
  onFocus?: (e: any) => void;
  
  // 其他props
  [key: string]: any;
}

export const LyyNumberInput: React.FC<LyyNumberInputProps> = ({
  compId,
  field,
  label,
  placeholder = '请输入',
  defaultValue = '',
  required = false,
  disabled = false,
  readonly = false,
  min,
  max,
  step,
  precision = 2,
  formId,
  validateTrigger = 'change',
  rules = [],
  style,
  onChange,
  onBlur,
  onFocus,
  ...props
}) => {
  // 使用字段ID或组件ID作为唯一标识
  const fieldId = field ? `${compId}.${field}` : compId;
  
  // 订阅表单数据变化
  const { formData } = useSnapshot(rootStore);
  const fieldData = formData.fields[fieldId];
  
  // 本地状态用于输入过程中的临时值
  const [inputValue, setInputValue] = useState<string>('');
  const [isFocused, setIsFocused] = useState(false);

  // 初始化字段
  useEffect(() => {
    if (!fieldData) {
      formDataActions.initField(fieldId, {
        type: 'number',
        required,
        placeholder,
        defaultValue
      }, defaultValue);
    }
  }, [fieldId, required, placeholder, defaultValue]);

  // 同步显示值
  useEffect(() => {
    if (!isFocused && fieldData) {
      setInputValue(String(fieldData.value || ''));
    }
  }, [fieldData?.value, isFocused]);

  // 处理输入变化
  const handleInput = (e: any) => {
    const rawValue = e.detail.value;
    setInputValue(rawValue);
    
    // 实时验证和更新
    if (validateTrigger === 'change') {
      const numericValue = rawValue === '' ? '' : Number(rawValue);
      
      // 调试信息：打印更新操作
      if (process.env.NODE_ENV === 'development') {
        console.log(`🔢 [LyyNumberInput ${compId}] 更新字段:`, {
          fieldId,
          field,
          value: numericValue,
          rawValue,
          formId
        });
      }
      
      // 更新到store
      formDataActions.updateFieldValue(fieldId, numericValue, true);
      
      // 触发回调
      onChange?.(numericValue, e);
    }
  };

  // 处理失焦事件
  const handleBlur = (e: any) => {
    setIsFocused(false);
    
    const rawValue = inputValue;
    const numericValue = rawValue === '' ? '' : Number(rawValue);
    
    // 数值格式化
    let formattedValue = numericValue;
    if (typeof numericValue === 'number' && !isNaN(numericValue)) {
      // 应用精度
      if (precision >= 0) {
        formattedValue = Number(numericValue.toFixed(precision));
      }
      
      // 应用范围限制
      if (min !== undefined && formattedValue < min) {
        formattedValue = min;
      }
      if (max !== undefined && formattedValue > max) {
        formattedValue = max;
      }
    }
    
    // 更新最终值
    const shouldValidate = validateTrigger === 'blur' || validateTrigger === 'change';
    formDataActions.updateFieldValue(fieldId, formattedValue, shouldValidate);
    
    // 更新输入框显示
    setInputValue(String(formattedValue || ''));
    
    // 触发回调
    onBlur?.(formattedValue, e);
  };

  // 处理聚焦事件
  const handleFocus = (e: any) => {
    setIsFocused(true);
    onFocus?.(e);
  };

  // 渲染错误信息
  const renderErrors = () => {
    if (!fieldData || !fieldData.errors || !Array.isArray(fieldData.errors) || fieldData.errors.length === 0) {
      return null;
    }

    return (
      <View style={{ marginTop: '4px' }}>
        {fieldData.errors.map((error, index) => (
          <Text 
            key={index} 
            style={{ 
              color: '#ff4d4f', 
              fontSize: '12px',
              lineHeight: '16px'
            }}
          >
            {error}
          </Text>
        ))}
      </View>
    );
  };

  // 计算输入框样式
  const inputStyle = {
    ...style,
    ...(fieldData && !fieldData.isValid ? { borderColor: '#ff4d4f' } : {})
  };

  return (
    <View style={{ marginBottom: '10px' }}>
      {/* 标签 */}
      <View style={{ marginBottom: '4px', display: 'flex', alignItems: 'center' }}>
        <Text style={{ fontSize: '14px', color: '#333' }}>
          {label || field || '数字输入框'}
        </Text>
        {required && (
          <Text style={{ color: '#ff4d4f', marginLeft: '2px' }}>*</Text>
        )}
      </View>

      {/* 输入框 */}
      <Input
        type="number"
        value={inputValue}
        onInput={handleInput}
        onBlur={handleBlur}
        onFocus={handleFocus}
        placeholder={placeholder}
        disabled={disabled}
        style={inputStyle}
        {...props}
      />

      {/* 错误信息 */}
      {renderErrors()}
      
      {/* 开发模式调试信息 */}
      {process.env.NODE_ENV === 'development' && fieldData && (
        <View style={{ 
          marginTop: '4px',
          padding: '4px',
          backgroundColor: '#f0f0f0',
          borderRadius: '2px'
        }}>
          <Text style={{ fontSize: '10px', color: '#666' }}>
            ID: {fieldId} | 值: {JSON.stringify(fieldData.value)} | 
            有效: {fieldData.isValid ? '是' : '否'}
          </Text>
        </View>
      )}
    </View>
  );
};

export default LyyNumberInput;
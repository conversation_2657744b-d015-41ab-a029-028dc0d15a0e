import React from 'react';
import { View, Text, Input } from '@tarojs/components';

// 容器组件
export const LyyContainer: React.FC<any> = (props) => {
  return (
    <View style={{ padding: '10px' }} {...props}>
      {props.children}
    </View>
  );
};

// 表单组件
export const LyyForm: React.FC<any> = (props) => {
  return (
    <View style={{ marginBottom: '20px' }} {...props}>
      {props.children}
    </View>
  );
};

// 导出增强的数字输入框组件
export { LyyNumberInput } from './LyyNumberInput';

// 导出增强的计算值组件
export { LyyFormula } from './LyyFormula';

// 按钮组件
export const LyyButton: React.FC<any> = (props) => {
  console.log('Button props:', props);
  return (
    <View style={{ marginBottom: '10px' }}>
      <View
        style={{
          padding: '10px',
          backgroundColor: '#007AFF',
          color: 'white',
          textAlign: 'center',
          borderRadius: '5px'
        }}
        {...props}
      >
        {props.prop?.text || '按钮'}
      </View>
    </View>
  );
};
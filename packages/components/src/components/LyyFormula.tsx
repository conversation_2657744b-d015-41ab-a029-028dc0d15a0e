import React, { useState, useEffect, useRef } from 'react';
import { View, Text } from '@tarojs/components';
import { useSnapshot } from 'valtio';
import { 
  formulaProcessor, 
  formDataActions,
  rootStore,
  type FormulaConfig, 
  type FormulaResult 
} from '@lyy/core';

/**
 * LyyFormula 增强计算值组件
 * 集成增强表达式引擎，支持依赖追踪和响应式更新
 */

export interface LyyFormulaProps {
  // 组件基础属性
  compId: string;
  field: string;
  label?: string;
  
  // 表达式配置
  exp: string;
  type?: 'text' | 'number' | 'currency';
  defaultValue?: any;
  placeholder?: string;
  
  // 管道配置
  pipes?: Array<{
    pipe: string;
    option: Record<string, any>;
  }>;

  // 表单ID（用于作用域）
  formId?: string;
  
  // 样式
  style?: React.CSSProperties;
  
  // 其他props
  [key: string]: any;
}

export const LyyFormula: React.FC<LyyFormulaProps> = ({
  compId,
  field,
  label,
  exp,
  type = 'text',
  defaultValue = '',
  placeholder = '',
  pipes = [],
  formId,
  style,
  ...props
}) => {
  // 订阅表单数据变化
  const { formData } = useSnapshot(rootStore);
  
  const [result, setResult] = useState<FormulaResult>({
    value: defaultValue,
    displayValue: String(defaultValue || ''),
    dependencies: [],
    isValid: true
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  // 构建公式配置
  const formulaConfig: FormulaConfig = {
    field,
    label: label || '计算值',
    exp,
    type,
    pipes,
    formId,
    placeholder,
    defaultValue
  };

  // 初始化和订阅依赖变化
  useEffect(() => {
    if (!exp?.trim()) {
      setResult({
        value: defaultValue,
        displayValue: String(defaultValue || placeholder || ''),
        dependencies: [],
        isValid: true
      });
      return;
    }

    setIsLoading(true);

    // 构建表达式上下文 - 按formId构建嵌套结构
    const context = {
      currentComponent: {
        entityId: compId,
        compId,
        field,
        formId
      },
      enableCache: true,
      // 提供表单数据作为作用域
      scope: (() => {
        const scope: Record<string, any> = {};
        
        // 按formId分组构建嵌套结构
        const formGroups: Record<string, Record<string, any>> = {};
        
        Object.keys(formData.fields).forEach(fieldId => {
          const fieldData = formData.fields[fieldId];
          
          // 解析字段ID: compId.field
          if (fieldId.includes('.')) {
            const [componentId, fieldName] = fieldId.split('.');
            // 查找这个组件属于哪个表单
            // 这里简化处理，假设所有字段都属于同一个表单formId
            const targetFormId = formId || 'defaultForm';
            
            if (!formGroups[targetFormId]) {
              formGroups[targetFormId] = { modelValue: {} };
            }
            
            // 构建 formId.modelValue.fieldName 结构
            formGroups[targetFormId].modelValue[fieldName] = fieldData.value;
            
            // 同时支持扁平访问 fieldName
            scope[fieldName] = fieldData.value;
          } else {
            // 非字段类型的组件
            scope[fieldId] = fieldData.value;
          }
        });
        
        // 将分组后的表单数据合并到scope
        Object.keys(formGroups).forEach(formGroupId => {
          scope[formGroupId] = formGroups[formGroupId];
        });
        
        // 提供完整的formData访问
        scope.formData = formData.fields;
        
        // 调试信息：打印构建的上下文
        if (process.env.NODE_ENV === 'development') {
          console.log(`🔧 [LyyFormula ${compId}] 构建上下文:`, JSON.stringify(scope, null, 2));
          console.log(`📊 [LyyFormula ${compId}] 表达式: "${exp}"`);
          console.log(`📝 [LyyFormula ${compId}] formData.fields:`, Object.keys(formData.fields));
        }
        
        return scope;
      })()
    };

    // 订阅公式变化
    const unsubscribe = formulaProcessor.subscribe(
      compId,
      formulaConfig,
      (newResult: FormulaResult) => {
        setResult(newResult);
        setIsLoading(false);
        
        if (newResult.error) {
          console.error(`Formula组件 [${compId}] 计算错误:`, newResult.error);
        }
      },
      context
    );

    unsubscribeRef.current = unsubscribe;

    // 清理订阅
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [compId, exp, type, JSON.stringify(pipes), formId, formData]); // 添加formData依赖

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, []);

  // 渲染错误状态
  const renderError = () => (
    <View style={{ 
      marginBottom: '10px', 
      padding: '8px',
      backgroundColor: '#ffebee',
      borderRadius: '4px',
      ...style 
    }}>
      <Text style={{ color: '#c62828', fontSize: '12px' }}>
        {label || '计算值'}: 表达式错误
      </Text>
      {result.error && (
        <Text style={{ color: '#9e9e9e', fontSize: '10px', marginTop: '4px' }}>
          {result.error.message}
        </Text>
      )}
    </View>
  );

  // 渲染加载状态
  const renderLoading = () => (
    <View style={{ 
      marginBottom: '10px',
      ...style 
    }}>
      <Text style={{ color: '#666' }}>
        {label || '计算值'}: 计算中...
      </Text>
    </View>
  );

  // 渲染正常状态
  const renderValue = () => (
    <View style={{ 
      marginBottom: '10px',
      ...style 
    }}>
      <Text>
        {label || '计算值'}: {result.displayValue || placeholder}
      </Text>
      
      {/* 开发模式下显示调试信息 */}
      {process.env.NODE_ENV === 'development' && result.dependencies.length > 0 && (
        <Text style={{ 
          color: '#9e9e9e', 
          fontSize: '10px', 
          marginTop: '4px' 
        }}>
          依赖: {result.dependencies.join(', ')}
        </Text>
      )}
    </View>
  );

  // 根据状态渲染不同内容
  if (!result.isValid && result.error) {
    return renderError();
  }

  if (isLoading) {
    return renderLoading();
  }

  return renderValue();
};

export default LyyFormula;
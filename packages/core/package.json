{"name": "@lyy/core", "version": "1.0.0", "description": "", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.mts", "bin": {"generate-code": "./dist/cli.js"}, "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "echo \"Error: no test specified\" && exit 1", "prepare": "npm run build"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@babel/core": "^7.28.0", "@babel/generator": "^7.28.0", "@babel/parser": "^7.28.0", "@babel/traverse": "^7.28.0", "@babel/types": "^7.28.2", "jsep": "^1.4.0", "react": "^18.3.3", "valtio": "^2.1.5"}, "devDependencies": {"@types/babel__core": "^7.20.5", "@types/babel__generator": "^7.27.0", "@types/babel__parser": "^7.1.5", "@types/babel__traverse": "^7.28.0", "@types/minimatch": "^6.0.0", "@types/node": "^20.0.0", "tsup": "^8.5.0", "typescript": "^5.0.0"}}
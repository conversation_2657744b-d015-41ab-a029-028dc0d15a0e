{
  "compilerOptions": {
    "target": "esnext", // 或者其他合适的目标（例如 es6）
    "module": "esnext", // 使用 ES 模块
    "moduleResolution": "node", // 模块解析模式
    "jsx": "react", // 启用 JSX 支持
    "esModuleInterop": true, // 允许与非 ES 模块的交互
    "skipLibCheck": true, // 跳过库文件的类型检查（提高编译速度）
    "strict": false, // 启用严格模式
    "declaration": true, // 生成 .d.ts 声明文件
    "outDir": "./dist", // 输出目录
    "allowJs": true, // 允许 JavaScript 文件存在
    "types": ["node", "react"]
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist"
  ]
}
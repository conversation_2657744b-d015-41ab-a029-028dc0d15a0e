import { defineConfig } from 'tsup';

export default defineConfig({
  entry: ['src/**/*.ts'],
  format: ['esm'],  // 输出 ESM 格式
  external: ['react', 'react-dom'], // 确保 React 被视为外部依赖
  dts: {  // 如果需要生成类型定义文件
    entry: ['src/index.ts', 'src/build.ts'], // 设置多个入口
    resolve: true, // 解析类型定义
  },
  clean: true,
  outExtension({ format }) {
    return {
      js: '.js' // 不管是 esm 还是 cjs，都输出为 .js
    }
  }
});

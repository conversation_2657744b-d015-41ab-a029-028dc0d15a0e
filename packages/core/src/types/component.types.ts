import React from "react";

// 基础组件类型
export interface BaseComponent {
  compName?: string;
  compId?: string;
  compNameCn?: string;
  entityId: string;
  prop?: Record<string, any>;
  actions?: ComponentAction[];
  childrens?: ComponentNode[];
  modelValue?: any;
  style?: React.CSSProperties;
}

// 组件节点类型
export interface ComponentNode extends BaseComponent {
  _compName?: string;
  chosen?: boolean;
  selected?: boolean;
  compPropName?: string;
  prop?: {
    [key: string]: any
  };
  validationResult?: {
    isValid: boolean;
    errors: readonly string[];
  };
}

// 组件动作类型
export interface ComponentAction {
  event: string;
  action: string;
  option: ActionOption;
  thenActions?: any[];
}

// 动作选项类型
export interface ActionOption {
  url: string;
  method: string;
  responseType: string;
  payloads: {
    type: string;
    dynamic: {
      nodePath: string;
    };
  };
  responseDataKey: string;
  customOption: {
    loading: boolean;
    nextEventsDelay: number;
    parse: boolean;
    unhandled: boolean;
  };
  headerPayloads: any[];
  interceptors: {
    requestInterceptor: string;
    responseInterceptor: string;
  };
}

// 解析后的组件树
export interface ParsedComponentTree {
  entityId: string;
  compName: string;
  compNameCn?: string;
  compId?: string;
  prop?: Record<string, any>;
  eventHandlers?: Record<string, string>;
  children?: ParsedComponentTree[];
  actions?: ComponentAction[];
}
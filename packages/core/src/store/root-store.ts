import { proxy } from 'valtio';
import { ComponentNode } from '../types/component.types';
import { performanceTools } from '../utils/performance';

/**
 * 全局状态根存储 - 三层架构设计
 * 基于Gemini架构建议和Valtio最佳实践
 */

// ========== 类型定义 ==========

// 页面配置类型
export interface PageConfig {
  title: string;
  backgroundColor?: string;
  padding?: string;
  [key: string]: any;
}

// API数据存储类型
export interface ApiDataState {
  loading: boolean;
  data: any;
  error: Error | null;
  timestamp?: number;
}

// 全局环境配置类型
export interface GlobalEnv {
  device: 'desktop' | 'mobile' | 'tablet';
  theme: 'light' | 'dark';
  language: string;
  platform?: 'h5' | 'weapp' | 'alipay' | 'swan';
}

// 用户信息类型
export interface UserInfo {
  id: string;
  name: string;
  permissions: string[];
  roles?: string[];
}

// UI状态类型 (编辑器专用)
export interface UIState {
  selectedComponentId: string | null;
  isSaving: boolean;
  isPreviewMode: boolean;
  rightPanelTab: 'props' | 'events' | 'style' | 'data';
  leftPanelCollapsed: boolean;
  rightPanelCollapsed: boolean;
}

// 表单字段类型
export interface FormField {
  value: any;
  isValid: boolean;
  errors: string[];
  metadata?: {
    type: 'text' | 'number' | 'email' | 'date' | 'select' | 'checkbox';
    required: boolean;
    placeholder?: string;
    defaultValue?: any;
  };
}

// 表单数据状态类型
export interface FormDataState {
  // 基于组件ID的字段数据存储
  fields: Record<string, FormField>;
  // 表单级别的验证状态
  forms: Record<string, {
    isValid: boolean;
    isDirty: boolean;
    errors: string[];
    touched: Record<string, boolean>; // 追踪哪些字段被触摸过
  }>;
  // 计算值缓存 (formula组件的结果)
  computedValues: Record<string, any>;
}

// 根状态类型
export interface RootStoreState {
  page: {
    config: PageConfig;
    components: Record<string, ComponentNode>;
  };
  global: {
    user: UserInfo;
    apiData: Record<string, ApiDataState>;
    env: GlobalEnv;
    commonData: Record<string, any>; // 公共数据存储
  };
  ui: UIState;
  // 新增：表单数据状态
  formData: FormDataState;
}

// ========== 状态初始化 ==========

/**
 * 创建根状态存储
 * 使用Valtio proxy创建响应式状态树
 */
export const rootStore = proxy<RootStoreState>({
  /**
   * 第一层：page状态层 - "静态蓝图"
   * 描述页面结构和组件配置
   */
  page: {
    // 页面全局配置
    config: {
      title: '低代码页面',
      backgroundColor: '#ffffff',
      padding: '0'
    },
    
    // 扁平化组件存储 - 核心性能优化
    // 使用ID字典而非嵌套结构，实现O(1)查找
    components: {}
  },

  /**
   * 第二层：global状态层 - "共享上下文"
   * 存储跨页面、跨组件的应用级数据
   */
  global: {
    // 用户信息
    user: {
      id: '',
      name: '',
      permissions: []
    },
    
    // API响应数据存储
    // key为responseDataKey，value为API响应状态
    apiData: {},
    
    // 环境配置
    env: {
      device: 'desktop',
      theme: 'light', 
      language: 'zh-CN'
    },
    
    // 公共数据存储 (如全局变量、配置等)
    commonData: {}
  },

  /**
   * 第三层：ui状态层 - "临时画布"  
   * 编辑器UI状态，不影响最终生成的应用
   */
  ui: {
    selectedComponentId: null,
    isSaving: false,
    isPreviewMode: false,
    rightPanelTab: 'props',
    leftPanelCollapsed: false,
    rightPanelCollapsed: false
  },

  /**
   * 表单数据状态层 - "动态数据存储"
   * 存储所有表单组件的值和状态，支持响应式更新
   */
  formData: {
    // 字段数据存储
    fields: {},
    
    // 表单验证状态
    forms: {},
    
    // 计算值缓存
    computedValues: {}
  }
});

// ========== 状态操作函数 ==========

/**
 * 页面级操作
 */
export const pageActions = {
  // 初始化页面组件
  initComponents(components: Record<string, ComponentNode>) {
    console.log('初始化页面组件:', Object.keys(components).length);
    rootStore.page.components = components;
  },

  // 注册单个组件
  registerComponent(component: ComponentNode) {
    if (!component.entityId) {
      console.error('组件entityId不能为空');
      return;
    }
    
    console.log(`注册组件: ${component.entityId}`);
    rootStore.page.components[component.entityId] = component;
  },

  // 更新组件属性 (支持时间旅行追踪)
  updateComponentProp(entityId: string, propPath: string, value: any, actionType = 'updateProp') {
    const component = rootStore.page.components[entityId];
    if (!component) {
      console.warn(`组件 ${entityId} 不存在`);
      return;
    }

    // 支持深层路径更新，如 'prop.disabled' 或 'state.value'
    const pathArray = propPath.split('.');
    let target = component;
    
    // 记录变更前状态用于时间旅行调试
    const oldValue = pathArray.reduce((obj, key, index) => {
      if (index === pathArray.length - 1) {
        return obj?.[key];
      }
      return obj?.[key];
    }, component);

    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
      // 动态导入避免服务端渲染问题
      import('./time-travel-store').then(({ timeTravel }) => {
        timeTravel.logAction({
          type: actionType,
          params: { entityId, propPath, value, oldValue },
          description: `更新组件 ${entityId}.${propPath}`,
          entityId
        });
      });
    }
    
    for (let i = 0; i < pathArray.length - 1; i++) {
      const key = pathArray[i];
      if (!target[key]) {
        target[key] = {};
      }
      target = target[key];
    }
    
    const finalKey = pathArray[pathArray.length - 1];
    target[finalKey] = value;
    
    console.log(`更新组件 ${entityId}.${propPath}:`, value);
  },

  // 获取组件 (响应式)
  getComponent(entityId: string): ComponentNode | undefined {
    return rootStore.page.components[entityId];
  }
};

/**
 * 全局级操作
 */  
export const globalActions = {
  // 设置用户信息
  setUser(user: Partial<UserInfo>) {
    // 时间旅行调试记录
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
      import('./time-travel-store').then(({ timeTravel }) => {
        timeTravel.logAction({
          type: 'setUser',
          params: { user, oldUser: { ...rootStore.global.user } },
          description: `设置用户信息: ${user.name || user.id || 'Unknown'}`
        });
      });
    }
    
    Object.assign(rootStore.global.user, user);
    console.log('设置用户信息:', user);
  },

  // 设置API数据
  setApiData(key: string, data: Partial<ApiDataState>) {
    if (!rootStore.global.apiData[key]) {
      rootStore.global.apiData[key] = {
        loading: false,
        data: null,
        error: null
      };
    }
    
    // 时间旅行调试记录
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
      import('./time-travel-store').then(({ timeTravel }) => {
        timeTravel.logAction({
          type: 'setApiData',
          params: { key, data, oldData: { ...rootStore.global.apiData[key] } },
          description: `设置API数据: ${key}`
        });
      });
    }
    
    Object.assign(rootStore.global.apiData[key], {
      ...data,
      timestamp: Date.now()
    });
    
    console.log(`设置API数据 ${key}:`, data);
  },

  // 获取API数据
  getApiData(key: string): ApiDataState | undefined {
    return rootStore.global.apiData[key];
  },

  // 设置公共数据
  setCommonData(key: string, value: any) {
    rootStore.global.commonData[key] = value;
    console.log(`设置公共数据 ${key}:`, value);
  }
};

/**
 * 表单数据操作
 */
export const formDataActions = {
  // 初始化字段
  initField(fieldId: string, metadata: FormField['metadata'], initialValue?: any) {
    const value = initialValue !== undefined ? initialValue : metadata?.defaultValue ?? '';
    
    rootStore.formData.fields[fieldId] = {
      value,
      isValid: true,
      errors: [],
      metadata
    };

    console.log(`初始化字段 ${fieldId}:`, value);
  },

  // 更新字段值
  updateFieldValue(fieldId: string, value: any, validate: boolean = true) {
    const field = rootStore.formData.fields[fieldId];
    if (!field) {
      console.warn(`字段 ${fieldId} 不存在，正在自动创建`);
      this.initField(fieldId, { type: 'text', required: false }, value);
      return;
    }

    // 更新值
    field.value = value;
    
    // 标记表单为脏状态
    const formId = this.getFormIdByFieldId(fieldId);
    if (formId) {
      const formState = rootStore.formData.forms[formId];
      if (formState) {
        formState.isDirty = true;
        formState.touched[fieldId] = true;
      }
    }

    // 验证字段
    if (validate) {
      this.validateField(fieldId);
    }

    console.log(`更新字段 ${fieldId}:`, value);
    
    // 通知formula组件重新计算
    this.invalidateComputedValues();
  },

  // 验证单个字段
  validateField(fieldId: string): boolean {
    const field = rootStore.formData.fields[fieldId];
    if (!field) return false;

    const errors: string[] = [];
    const { value, metadata } = field;

    // 必填验证
    if (metadata?.required && (value === '' || value === null || value === undefined)) {
      errors.push('此字段为必填项');
    }

    // 类型验证
    if (metadata?.type === 'email' && value && !this.isValidEmail(value)) {
      errors.push('请输入有效的邮箱地址');
    }

    if (metadata?.type === 'number' && value && isNaN(Number(value))) {
      errors.push('请输入有效的数字');
    }

    // 从组件的props中获取更详细的验证规则
    const component = rootStore.page.components[fieldId];
    if (component && component.props && component.props.rules) {
      for (const rule of component.props.rules) {
        if (rule.required && (value === '' || value === null || value === undefined)) {
          errors.push(rule.message || '此字段为必填项');
        }
        if (rule.min !== undefined && Number(value) < rule.min) {
          errors.push(rule.message || `值不能小于 ${rule.min}`);
        }
        if (rule.max !== undefined && Number(value) > rule.max) {
          errors.push(rule.message || `值不能大于 ${rule.max}`);
        }
      }
    }

    // 更新字段状态
    field.errors = errors;
    field.isValid = errors.length === 0;

    return field.isValid;
  },

  // 验证整个表单
  validateForm(formId: string): boolean {
    const formState = rootStore.formData.forms[formId];
    if (!formState) return true;

    const fieldIds = this.getFieldIdsByFormId(formId);
    let allValid = true;
    const formErrors: string[] = [];

    // 验证每个字段
    fieldIds.forEach(fieldId => {
      const isFieldValid = this.validateField(fieldId);
      if (!isFieldValid) {
        allValid = false;
        const field = rootStore.formData.fields[fieldId];
        formErrors.push(...field.errors);
      }
    });

    // 更新表单状态
    formState.isValid = allValid;
    formState.errors = formErrors;

    console.log(`验证表单 ${formId}:`, allValid ? '通过' : '失败');
    return allValid;
  },

  // 初始化表单
  initForm(formId: string, fieldIds: string[] = []) {
    rootStore.formData.forms[formId] = {
      isValid: true,
      isDirty: false,
      errors: [],
      touched: {}
    };

    // 初始化touched状态
    fieldIds.forEach(fieldId => {
      rootStore.formData.forms[formId].touched[fieldId] = false;
    });

    console.log(`初始化表单 ${formId}`);
  },

  // 重置表单
  resetForm(formId: string) {
    const fieldIds = this.getFieldIdsByFormId(formId);
    
    // 重置字段值到默认值
    fieldIds.forEach(fieldId => {
      const field = rootStore.formData.fields[fieldId];
      if (field) {
        field.value = field.metadata?.defaultValue ?? '';
        field.isValid = true;
        field.errors = [];
      }
    });

    // 重置表单状态
    const formState = rootStore.formData.forms[formId];
    if (formState) {
      formState.isValid = true;
      formState.isDirty = false;
      formState.errors = [];
      Object.keys(formState.touched).forEach(fieldId => {
        formState.touched[fieldId] = false;
      });
    }

    console.log(`重置表单 ${formId}`);
  },

  // 获取字段值
  getFieldValue(fieldId: string): any {
    return rootStore.formData.fields[fieldId]?.value;
  },

  // 获取表单数据
  getFormData(formId: string): Record<string, any> {
    const fieldIds = this.getFieldIdsByFormId(formId);
    const formData: Record<string, any> = {};

    fieldIds.forEach(fieldId => {
      const field = rootStore.formData.fields[fieldId];
      if (field) {
        // 使用metadata中的实际字段名，如果没有则使用fieldId
        const fieldName = field.metadata?.placeholder || fieldId;
        formData[fieldName] = field.value;
      }
    });

    return formData;
  },

  // 设置计算值
  setComputedValue(key: string, value: any) {
    rootStore.formData.computedValues[key] = value;
  },

  // 获取计算值
  getComputedValue(key: string): any {
    return rootStore.formData.computedValues[key];
  },

  // 清理计算值缓存
  invalidateComputedValues() {
    rootStore.formData.computedValues = {};
  },

  // 辅助方法：根据字段ID获取表单ID
  getFormIdByFieldId(fieldId: string): string | null {
    // 这里需要根据实际的组件树结构来实现
    // 临时实现：从组件存储中查找
    const components = rootStore.page.components;
    for (const [componentId, component] of Object.entries(components)) {
      if (component.compName === 'lyy-form' && 
          component.childrens?.some(child => child.entityId === fieldId)) {
        return componentId;
      }
    }
    return null;
  },

  // 辅助方法：根据表单ID获取字段ID列表
  getFieldIdsByFormId(formId: string): string[] {
    const formComponent = rootStore.page.components[formId];
    if (!formComponent || formComponent.compName !== 'lyy-form') {
      return [];
    }

    return formComponent.childrens?.map(child => child.entityId).filter(Boolean) || [];
  },

  // 邮箱验证
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
};

/**
 * UI级操作 (编辑器专用)
 */
export const uiActions = {
  // 选择组件
  selectComponent(entityId: string | null) {
    rootStore.ui.selectedComponentId = entityId;
  },

  // 切换预览模式
  togglePreviewMode() {
    rootStore.ui.isPreviewMode = !rootStore.ui.isPreviewMode;
  },

  // 设置右侧面板tab
  setRightPanelTab(tab: UIState['rightPanelTab']) {
    rootStore.ui.rightPanelTab = tab;
  },

  // 设置保存状态
  setSaving(saving: boolean) {
    rootStore.ui.isSaving = saving;
  }
};

// ========== 工具函数 ==========

/**
 * 根据路径获取状态值 (支持表达式引擎)
 */
export function getValueByPath(path: string): any {
  // 特殊处理表单数据的快捷访问
  // 支持 formData.fieldId 或 fieldId 直接访问字段值
  if (path.startsWith('formData.')) {
    const fieldId = path.replace('formData.', '');
    return formDataActions.getFieldValue(fieldId);
  }
  
  // 支持直接使用fieldId访问表单字段值
  if (rootStore.formData.fields[path]) {
    return rootStore.formData.fields[path].value;
  }

  // 原有的路径解析逻辑
  const pathArray = path.split('.');
  let current = rootStore as any;
  
  for (const key of pathArray) {
    if (current && typeof current === 'object' && key in current) {
      current = current[key];
    } else {
      return undefined;
    }
  }
  
  return current;
}

/**
 * 根据路径设置状态值
 */
export function setValueByPath(path: string, value: any): void {
  const pathArray = path.split('.');
  let current = rootStore;
  
  for (let i = 0; i < pathArray.length - 1; i++) {
    const key = pathArray[i];
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    }
    current = current[key];
  }
  
  const finalKey = pathArray[pathArray.length - 1];
  current[finalKey] = value;
}

// ========== 调试工具 ==========

/**
 * 获取根状态的快照 (用于调试)
 */
export function getRootSnapshot() {
  const { snapshot } = require('valtio');
  return snapshot(rootStore);
}

/**
 * 打印状态统计信息
 */
export function printStoreStats() {
  const stats = {
    componentCount: Object.keys(rootStore.page.components).length,
    apiDataCount: Object.keys(rootStore.global.apiData).length,
    commonDataCount: Object.keys(rootStore.global.commonData).length,
    selectedComponent: rootStore.ui.selectedComponentId,
    previewMode: rootStore.ui.isPreviewMode
  };
  
  console.table(stats);
  return stats;
}

// ========== 性能优化集成 ==========

/**
 * 初始化性能优化工具
 */
const { batchUpdateManager } = performanceTools;
const monitor = performanceTools.monitor();

// 设置批量更新处理器
batchUpdateManager.setUpdateHandler((componentId: string, updates: Record<string, any>) => {
  const component = rootStore.page.components[componentId];
  if (!component) {
    console.warn(`批量更新目标组件不存在: ${componentId}`);
    return;
  }

  // 应用批量更新
  Object.keys(updates).forEach(path => {
    const pathArray = path.split('.');
    let target = component;
    
    for (let i = 0; i < pathArray.length - 1; i++) {
      const key = pathArray[i];
      if (!target[key]) {
        target[key] = {};
      }
      target = target[key];
    }
    
    const finalKey = pathArray[pathArray.length - 1];
    target[finalKey] = updates[path];
  });
});

/**
 * 启用性能监控的组件属性更新
 */
export const optimizedPageActions = {
  ...pageActions,
  
  // 优化版本的updateComponentProp - 支持批量更新
  updateComponentProp(entityId: string, propPath: string, value: any, immediate = false) {
    if (immediate) {
      // 立即更新 (用于重要的UI状态)
      return pageActions.updateComponentProp(entityId, propPath, value);
    } else {
      // 批量更新 (用于一般的状态更新)
      const updates = { [propPath]: value };
      batchUpdateManager.addUpdate(entityId, updates);
    }
    
    // 记录性能指标
    monitor?.recordMetric('updateCount', entityId);
  },

  // 批量更新多个属性
  updateComponentProps(entityId: string, updates: Record<string, any>) {
    batchUpdateManager.addUpdate(entityId, updates);
    monitor?.recordMetric('updateCount', entityId);
  },

  // 强制刷新批量更新
  flushUpdates() {
    batchUpdateManager.forceFlush();
  }
};

/**
 * 获取性能报告
 */
export function getPerformanceReport() {
  return monitor?.getPerformanceReport() || {};
}

/**
 * 性能优化工具导出
 */
export const optimization = {
  batchUpdateManager,
  monitor,
  renderOptimizer: performanceTools.renderOptimizer,
  eventThrottleManager: performanceTools.eventThrottleManager,
  expressionCacheManager: performanceTools.expressionCacheManager
};

export default rootStore;
/**
 * 时间旅行调试系统 - 基于Valtio
 * 不改变现有状态管理，添加历史追踪功能
 */

import { snapshot } from 'valtio';
import { rootStore, type RootStoreState } from './root-store';

// ========== 类型定义 ==========

export interface StateSnapshot {
  id: string;
  timestamp: number;
  state: RootStoreState;
  actionId?: string;  // 关联的动作ID
  description?: string; // 变更描述
}

export interface ActionLog {
  id: string;
  timestamp: number;
  type: string;
  params: any;
  result?: any;
  duration?: number;
  entityId?: string;
  description: string;
}

export interface TimeTravelState {
  // 历史记录
  snapshots: StateSnapshot[];
  actionLogs: ActionLog[];
  
  // 当前位置
  currentSnapshotIndex: number;
  isTimeTravel: boolean;
  
  // 配置
  maxSnapshots: number;
  autoSnapshotInterval: number;
  isRecording: boolean;
}

// ========== 时间旅行存储 ==========

class TimeTravelStore {
  private state: TimeTravelState = {
    snapshots: [],
    actionLogs: [],
    currentSnapshotIndex: -1,
    isTimeTravel: false,
    maxSnapshots: 100,
    autoSnapshotInterval: 5000,
    isRecording: false
  };

  private autoSnapshotTimer?: NodeJS.Timeout;

  constructor() {
    // 仅在开发环境启用
    if (process.env.NODE_ENV === 'development') {
      this.startRecording();
    }
  }

  /**
   * 开始记录状态历史
   */
  startRecording(): void {
    if (this.state.isRecording) return;
    
    console.log('🎬 开始时间旅行调试记录');
    this.state.isRecording = true;

    // 创建初始快照
    this.createSnapshot('初始状态');

    // 设置自动快照定时器
    this.autoSnapshotTimer = setInterval(() => {
      this.createSnapshot('自动快照');
    }, this.state.autoSnapshotInterval);
  }

  /**
   * 停止记录
   */
  stopRecording(): void {
    if (!this.state.isRecording) return;
    
    console.log('⏹️ 停止时间旅行调试记录');
    this.state.isRecording = false;

    if (this.autoSnapshotTimer) {
      clearInterval(this.autoSnapshotTimer);
    }
  }

  /**
   * 创建状态快照
   */
  createSnapshot(description = '手动快照', actionId?: string): StateSnapshot {
    const snapshotData: StateSnapshot = {
      id: `snapshot_${Date.now()}_${Math.random().toString(36).slice(2)}`,
      timestamp: Date.now(),
      state: snapshot(rootStore) as RootStoreState,
      actionId,
      description
    };

    // 添加到历史记录
    this.state.snapshots.push(snapshotData);
    
    // 如果不在时间旅行模式，更新当前索引
    if (!this.state.isTimeTravel) {
      this.state.currentSnapshotIndex = this.state.snapshots.length - 1;
    }

    // 限制历史记录数量
    if (this.state.snapshots.length > this.state.maxSnapshots) {
      this.state.snapshots = this.state.snapshots.slice(-this.state.maxSnapshots);
      this.state.currentSnapshotIndex = Math.min(
        this.state.currentSnapshotIndex, 
        this.state.snapshots.length - 1
      );
    }

    console.log(`📸 创建快照: ${description} (${this.state.snapshots.length}/${this.state.maxSnapshots})`);
    return snapshotData;
  }

  /**
   * 记录动作日志
   */
  logAction(actionLog: Omit<ActionLog, 'id' | 'timestamp'>): ActionLog {
    const log: ActionLog = {
      id: `action_${Date.now()}_${Math.random().toString(36).slice(2)}`,
      timestamp: Date.now(),
      ...actionLog
    };

    this.state.actionLogs.push(log);
    
    // 限制动作日志数量
    if (this.state.actionLogs.length > this.state.maxSnapshots * 2) {
      this.state.actionLogs = this.state.actionLogs.slice(-this.state.maxSnapshots * 2);
    }

    // 如果这是一个重要动作，创建快照
    if (this.shouldCreateSnapshot(log)) {
      this.createSnapshot(log.description, log.id);
    }

    return log;
  }

  /**
   * 跳转到指定快照
   */
  jumpToSnapshot(index: number): boolean {
    if (index < 0 || index >= this.state.snapshots.length) {
      console.warn(`快照索引 ${index} 超出范围`);
      return false;
    }

    const targetSnapshot = this.state.snapshots[index];
    console.log(`⏰ 时间旅行到: ${targetSnapshot.description} (${new Date(targetSnapshot.timestamp).toLocaleString()})`);

    // 进入时间旅行模式
    this.state.isTimeTravel = true;
    this.state.currentSnapshotIndex = index;

    // 恢复状态（这里需要深度赋值，因为Valtio proxy的特殊性）
    this.restoreState(targetSnapshot.state);

    return true;
  }

  /**
   * 回到最新状态
   */
  backToLatest(): void {
    if (this.state.snapshots.length === 0) return;
    
    const latestIndex = this.state.snapshots.length - 1;
    this.jumpToSnapshot(latestIndex);
    
    // 退出时间旅行模式
    this.state.isTimeTravel = false;
    console.log('🔴 返回最新状态');
  }

  /**
   * 导出动作序列
   */
  exportActions(): ActionLog[] {
    return [...this.state.actionLogs];
  }

  /**
   * 重放动作序列
   */
  async replayActions(actions: ActionLog[], speed = 1000): Promise<void> {
    console.log(`🎮 开始重放 ${actions.length} 个动作`);
    
    for (const action of actions) {
      // 这里需要根据action.type重新执行对应的动作
      // 暂时只记录，具体实现需要与EventDispatcher集成
      console.log(`重放动作: ${action.description}`);
      
      if (speed > 0) {
        await new Promise(resolve => setTimeout(resolve, speed));
      }
    }
    
    console.log('✅ 动作重放完成');
  }

  /**
   * 获取调试信息
   */
  getDebugInfo() {
    return {
      snapshotCount: this.state.snapshots.length,
      actionCount: this.state.actionLogs.length,
      currentIndex: this.state.currentSnapshotIndex,
      isTimeTravel: this.state.isTimeTravel,
      isRecording: this.state.isRecording,
      latestSnapshot: this.state.snapshots[this.state.snapshots.length - 1],
      currentSnapshot: this.state.snapshots[this.state.currentSnapshotIndex]
    };
  }

  // ========== 私有方法 ==========

  private shouldCreateSnapshot(actionLog: ActionLog): boolean {
    // 重要动作类型自动创建快照
    const importantActions = ['setValue', 'request', 'validate', 'linkto'];
    return importantActions.includes(actionLog.type);
  }

  private restoreState(targetState: RootStoreState): void {
    // 深度恢复状态 - 需要小心处理Valtio proxy
    try {
      // 恢复page状态
      Object.assign(rootStore.page.config, targetState.page.config);
      Object.assign(rootStore.page.components, targetState.page.components);
      
      // 恢复global状态
      Object.assign(rootStore.global, targetState.global);
      
      // 恢复ui状态
      Object.assign(rootStore.ui, targetState.ui);
      
    } catch (error) {
      console.error('状态恢复失败:', error);
    }
  }
}

// ========== 导出单例 ==========

export const timeTravelStore = new TimeTravelStore();

// 便捷函数
export const timeTravel = {
  createSnapshot: (description?: string) => timeTravelStore.createSnapshot(description),
  logAction: (actionLog: Omit<ActionLog, 'id' | 'timestamp'>) => timeTravelStore.logAction(actionLog),
  jumpTo: (index: number) => timeTravelStore.jumpToSnapshot(index),
  backToLatest: () => timeTravelStore.backToLatest(),
  exportActions: () => timeTravelStore.exportActions(),
  replayActions: (actions: ActionLog[], speed?: number) => timeTravelStore.replayActions(actions, speed),
  getDebugInfo: () => timeTravelStore.getDebugInfo()
};

export default timeTravelStore;
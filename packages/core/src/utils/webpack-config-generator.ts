/**
 * Webpack配置生成器 - 支持代码分割
 * 为低代码平台生成优化的Webpack构建配置
 */

export interface WebpackCodeSplittingConfig {
  /** 启用代码分割 */
  enableSplitChunks: boolean;
  /** chunk大小限制(KB) */
  maxChunkSize: number;
  /** 最小chunk大小(KB) */
  minChunkSize: number;
  /** 最大异步请求数 */
  maxAsyncRequests: number;
  /** 最大初始请求数 */
  maxInitialRequests: number;
  /** 缓存组配置 */
  cacheGroups: Record<string, CacheGroupConfig>;
}

export interface CacheGroupConfig {
  name: string;
  test?: RegExp | string;
  chunks: 'all' | 'async' | 'initial';
  priority: number;
  minSize: number;
  maxSize?: number;
  reuseExistingChunk?: boolean;
}

/**
 * 生成支持代码分割的Webpack配置
 */
export function generateWebpackConfig(options: Partial<WebpackCodeSplittingConfig> = {}): any {
  const config: WebpackCodeSplittingConfig = {
    enableSplitChunks: true,
    maxChunkSize: 500 * 1024, // 500KB
    minChunkSize: 20 * 1024,  // 20KB
    maxAsyncRequests: 30,
    maxInitialRequests: 30,
    cacheGroups: {
      // 第三方库分组
      vendor: {
        name: 'vendor',
        test: /[\\/]node_modules[\\/]/,
        chunks: 'all',
        priority: 10,
        minSize: 0,
        reuseExistingChunk: true
      },
      // React相关库
      react: {
        name: 'react',
        test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
        chunks: 'all',
        priority: 20,
        minSize: 0
      },
      // Taro相关库
      taro: {
        name: 'taro',
        test: /[\\/]node_modules[\\/]@tarojs[\\/]/,
        chunks: 'all',
        priority: 15,
        minSize: 0
      },
      // 低代码平台组件库
      lyyComponents: {
        name: 'lyy-components',
        test: /[\\/]node_modules[\\/]@lyy[\\/]/,
        chunks: 'all',
        priority: 12,
        minSize: 0
      },
      // 公共代码
      common: {
        name: 'common',
        chunks: 'all',
        priority: 5,
        minSize: 20 * 1024,
        maxSize: 100 * 1024
      }
    },
    ...options
  };

  return {
    // 入口配置
    entry: {
      app: './src/app.ts'
    },

    // 输出配置
    output: {
      path: './dist',
      filename: '[name].[contenthash:8].js',
      chunkFilename: 'chunks/[name].[contenthash:8].js',
      publicPath: '/',
      clean: true
    },

    // 模块解析
    resolve: {
      extensions: ['.ts', '.tsx', '.js', '.jsx'],
      alias: {
        '@': './src',
        '@lyy/core': './packages/core/src',
        '@lyy/components': './packages/components/src'
      }
    },

    // 模块规则
    module: {
      rules: [
        {
          test: /\.(ts|tsx)$/,
          use: [
            {
              loader: 'babel-loader',
              options: {
                presets: [
                  ['@babel/preset-env', {
                    targets: { browsers: ['> 1%', 'last 2 versions'] },
                    modules: false,
                    useBuiltIns: 'usage',
                    corejs: 3
                  }],
                  '@babel/preset-react',
                  '@babel/preset-typescript'
                ],
                plugins: [
                  // 动态导入支持
                  '@babel/plugin-syntax-dynamic-import',
                  // React懒加载优化
                  ['@babel/plugin-transform-runtime', {
                    corejs: false,
                    helpers: true,
                    regenerator: true,
                    useESModules: false
                  }]
                ]
              }
            }
          ],
          exclude: /node_modules/
        },
        {
          test: /\.(scss|css)$/,
          use: [
            'style-loader',
            {
              loader: 'css-loader',
              options: {
                modules: {
                  localIdentName: '[name]__[local]--[hash:base64:5]'
                }
              }
            },
            'sass-loader'
          ]
        },
        {
          test: /\.(png|jpe?g|gif|svg)$/,
          type: 'asset',
          parser: {
            dataUrlCondition: {
              maxSize: 10 * 1024 // 10KB以下转为base64
            }
          },
          generator: {
            filename: 'images/[name].[hash:8][ext]'
          }
        }
      ]
    },

    // 代码分割配置
    optimization: {
      splitChunks: config.enableSplitChunks ? {
        chunks: 'all',
        minSize: config.minChunkSize,
        maxSize: config.maxChunkSize,
        maxAsyncRequests: config.maxAsyncRequests,
        maxInitialRequests: config.maxInitialRequests,
        cacheGroups: Object.fromEntries(
          Object.entries(config.cacheGroups).map(([key, group]) => [
            key,
            {
              test: group.test,
              name: group.name,
              chunks: group.chunks,
              priority: group.priority,
              minSize: group.minSize,
              maxSize: group.maxSize,
              reuseExistingChunk: group.reuseExistingChunk ?? true
            }
          ])
        )
      } : false,

      // 运行时代码分离
      runtimeChunk: {
        name: 'runtime'
      },

      // 最小化配置
      minimize: true,
      minimizer: [
        '...', // 使用默认minimizer
      ],

      // Tree shaking
      usedExports: true,
      sideEffects: false
    },

    // 插件配置
    plugins: [
      // 分析bundle大小
      ...(process.env.ANALYZE ? [
        // 动态加载webpack-bundle-analyzer
        (() => {
          try {
            const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
            return new BundleAnalyzerPlugin({
              analyzerMode: 'static',
              openAnalyzer: false,
              reportFilename: 'bundle-analysis.html'
            });
          } catch (e) {
            console.warn('webpack-bundle-analyzer未安装，跳过bundle分析');
            return null;
          }
        })()
      ].filter(Boolean) : []),

      // HTML模板
      (() => {
        try {
          const HtmlWebpackPlugin = require('html-webpack-plugin');
          return new HtmlWebpackPlugin({
            template: './src/index.html',
            chunks: ['runtime', 'vendor', 'app'],
            chunksSortMode: 'manual'
          });
        } catch (e) {
          console.warn('html-webpack-plugin未安装，跳过HTML模板生成');
          return null;
        }
      })(),

      // 预加载插件
      (() => {
        try {
          const PreloadWebpackPlugin = require('@vue/preload-webpack-plugin');
          return [
            new PreloadWebpackPlugin({
              rel: 'preload',
              include: 'initial'
            }),
            new PreloadWebpackPlugin({
              rel: 'prefetch',
              include: 'asyncChunks'
            })
          ];
        } catch (e) {
          console.warn('@vue/preload-webpack-plugin未安装，跳过预加载配置');
          return [];
        }
      })()
    ],

    // 开发服务器配置
    devServer: {
      port: 3000,
      hot: true,
      historyApiFallback: true,
      static: {
        directory: './public'
      }
    },

    // 性能配置
    performance: {
      hints: 'warning',
      maxEntrypointSize: 500 * 1024, // 500KB
      maxAssetSize: 300 * 1024       // 300KB
    },

    // 统计信息
    stats: {
      colors: true,
      modules: false,
      children: false,
      chunks: false,
      chunkModules: false
    }
  };
}

/**
 * 生成Taro特定的Webpack配置
 */
export function generateTaroWebpackConfig(options: Partial<WebpackCodeSplittingConfig> = {}): any {
  const baseConfig = generateWebpackConfig(options);

  return {
    ...baseConfig,

    // Taro特定的入口
    entry: {
      app: './src/app.ts'
    },

    // Taro特定的输出配置
    output: {
      ...baseConfig.output,
      filename: '[name].js',
      chunkFilename: '[name].js' // Taro不支持contenthash
    },

    // Taro特定的插件
    plugins: [
      ...baseConfig.plugins,
      
      // Taro webpack插件
      (() => {
        try {
          const taroChain = require('@tarojs/webpack-runner/dist/utils/chain').default;
          return taroChain({
            framework: 'react',
            ts: true,
            enableExtract: true
          });
        } catch (e) {
          console.warn('@tarojs/webpack-runner未安装，跳过Taro特定配置');
          return null;
        }
      })()
    ].filter(Boolean),

    // Taro特定的性能配置
    performance: {
      hints: false // Taro有自己的性能提示
    }
  };
}

/**
 * 生成代码分割清单
 */
export function generateChunkManifest(chunks: Map<string, string>): string {
  const manifest: Record<string, any> = {};
  
  chunks.forEach((code, chunkName) => {
    manifest[chunkName] = {
      filename: `${chunkName}.js`,
      size: Buffer.from(code, 'utf8').length,
      hash: require('crypto').createHash('md5').update(code).digest('hex').substring(0, 8)
    };
  });

  return JSON.stringify(manifest, null, 2);
}

/**
 * 生成预加载脚本
 */
export function generatePreloadScript(preloadManifest: string[]): string {
  const preloadScript = `
// 代码分割预加载脚本
(function() {
  'use strict';
  
  const PRELOAD_MANIFEST = ${JSON.stringify(preloadManifest)};
  const PRELOAD_STRATEGY = 'hover'; // hover, viewport, idle
  
  // 预加载状态跟踪
  const preloadedChunks = new Set();
  const preloadPromises = new Map();
  
  /**
   * 预加载chunk
   */
  function preloadChunk(chunkUrl) {
    if (preloadedChunks.has(chunkUrl)) {
      return Promise.resolve();
    }
    
    if (preloadPromises.has(chunkUrl)) {
      return preloadPromises.get(chunkUrl);
    }
    
    const promise = new Promise((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = chunkUrl;
      link.onload = () => {
        preloadedChunks.add(chunkUrl);
        resolve();
      };
      link.onerror = reject;
      document.head.appendChild(link);
    });
    
    preloadPromises.set(chunkUrl, promise);
    return promise;
  }
  
  /**
   * 批量预加载
   */
  function batchPreload(chunkUrls) {
    return Promise.allSettled(chunkUrls.map(preloadChunk));
  }
  
  /**
   * 智能预加载策略
   */
  function initPreloadStrategy() {
    switch (PRELOAD_STRATEGY) {
      case 'hover':
        // 鼠标悬停预加载
        document.addEventListener('mouseover', (e) => {
          const target = e.target.closest('[data-chunk]');
          if (target) {
            const chunkName = target.dataset.chunk;
            const chunkUrl = getChunkUrl(chunkName);
            if (chunkUrl) preloadChunk(chunkUrl);
          }
        });
        break;
        
      case 'viewport':
        // 进入视口预加载
        if ('IntersectionObserver' in window) {
          const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting && entry.target.dataset.chunk) {
                const chunkName = entry.target.dataset.chunk;
                const chunkUrl = getChunkUrl(chunkName);
                if (chunkUrl) preloadChunk(chunkUrl);
              }
            });
          }, { rootMargin: '50px' });
          
          document.querySelectorAll('[data-chunk]').forEach(el => {
            observer.observe(el);
          });
        }
        break;
        
      case 'idle':
        // 浏览器空闲时预加载
        if ('requestIdleCallback' in window) {
          requestIdleCallback(() => {
            batchPreload(PRELOAD_MANIFEST);
          }, { timeout: 5000 });
        } else {
          setTimeout(() => {
            batchPreload(PRELOAD_MANIFEST);
          }, 2000);
        }
        break;
    }
  }
  
  /**
   * 获取chunk URL
   */
  function getChunkUrl(chunkName) {
    return PRELOAD_MANIFEST.find(url => url.includes(chunkName));
  }
  
  // 页面加载完成后初始化预加载策略
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initPreloadStrategy);
  } else {
    initPreloadStrategy();
  }
  
  // 导出到全局供调试使用
  window.__CHUNK_PRELOADER__ = {
    preloadChunk,
    batchPreload,
    preloadedChunks,
    manifest: PRELOAD_MANIFEST
  };
})();
`;

  return preloadScript.trim();
}

export default {
  generateWebpackConfig,
  generateTaroWebpackConfig,
  generateChunkManifest,
  generatePreloadScript
};
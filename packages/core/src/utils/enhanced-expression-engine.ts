import jsep, { Expression, Literal, Identifier, MemberExpression, BinaryExpression, CallExpression, UnaryExpression, ConditionalExpression, ArrayExpression } from 'jsep';
import { rootStore, getValueByPath } from '../store/root-store';
import { performanceTools } from './performance';

/**
 * 增强表达式引擎 - 基于jsep的AST解析器
 * 支持依赖追踪、响应式更新、管道操作和安全执行
 * 根据Gemini建议实现
 */

// ========== 类型定义 ==========

export interface EnhancedExpressionContext {
  // 当前组件上下文
  currentComponent?: any;
  // 额外的上下文数据
  extraContext?: Record<string, any>;
  // 是否启用缓存
  enableCache?: boolean;
  // 表达式作用域（安全沙箱）
  scope?: Record<string, any>;
}

export interface ExpressionResult {
  value: any;
  error?: Error;
  dependencies: string[]; // 依赖的状态路径
  ast: Expression; // 解析的AST
}

export interface DependencyTracker {
  dependencies: Set<string>;
  addDependency(path: string): void;
  getDependencies(): string[];
  clear(): void;
}

// 管道函数类型
export type PipeFunction = (value: any, options?: Record<string, any>) => any;

// ========== 管道注册表 ==========

class PipeRegistry {
  private pipes = new Map<string, PipeFunction>();

  register(name: string, fn: PipeFunction) {
    this.pipes.set(name, fn);
  }

  get(name: string): PipeFunction | undefined {
    return this.pipes.get(name);
  }

  has(name: string): boolean {
    return this.pipes.has(name);
  }

  // 注册内置管道
  registerBuiltInPipes() {
    // 格式化货币
    this.register('formatCurrency', (value, options = {}) => {
      const { symbol = '¥', precision = 2 } = options;
      const num = Number(value);
      if (isNaN(num)) return value;
      return `${symbol}${num.toFixed(precision)}`;
    });

    // 添加文本
    this.register('append', (value, options = {}) => {
      const { text = '' } = options;
      return `${value}${text}`;
    });

    // 前置文本
    this.register('prepend', (value, options = {}) => {
      const { text = '' } = options;
      return `${text}${value}`;
    });

    // 格式化日期
    this.register('formatDate', (value, options = {}) => {
      const { format = 'YYYY-MM-DD HH:mm:ss' } = options;
      if (!value) return '';
      const date = new Date(value);
      if (isNaN(date.getTime())) return value;
      return this.formatDateString(date, format);
    });

    // 数字格式化
    this.register('formatNumber', (value, options = {}) => {
      const { precision = 2, thousandsSeparator = ',' } = options;
      const num = Number(value);
      if (isNaN(num)) return value;
      return num.toFixed(precision).replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSeparator);
    });
  }

  private formatDateString(date: Date, format: string): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return format
      .replace('YYYY', String(year))
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds);
  }
}

// ========== AST缓存 ==========

class ASTCache {
  private cache = new Map<string, Expression>();
  private maxSize = 1000;

  get(expression: string): Expression | undefined {
    return this.cache.get(expression);
  }

  set(expression: string, ast: Expression): void {
    // LRU简单实现：当缓存过大时清理
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(expression, ast);
  }

  clear(): void {
    this.cache.clear();
  }
}

// ========== 依赖追踪器 ==========

class DependencyTrackerImpl implements DependencyTracker {
  dependencies = new Set<string>();

  addDependency(path: string): void {
    this.dependencies.add(path);
  }

  getDependencies(): string[] {
    return Array.from(this.dependencies);
  }

  clear(): void {
    this.dependencies.clear();
  }
}

// ========== 增强表达式引擎 ==========

export class EnhancedExpressionEngine {
  private astCache = new ASTCache();
  private pipeRegistry = new PipeRegistry();
  private expressionCacheManager = performanceTools.expressionCacheManager;

  constructor() {
    this.initializeJsep();
    this.pipeRegistry.registerBuiltInPipes();
  }

  // 初始化jsep配置
  private initializeJsep() {
    // 添加管道操作符
    jsep.addBinaryOp('|', 1); // 最低优先级
  }

  /**
   * 解析表达式为AST
   */
  parse(expression: string): Expression {
    // 检查AST缓存
    const cachedAST = this.astCache.get(expression);
    if (cachedAST) {
      return cachedAST;
    }

    try {
      const ast = jsep(expression);
      this.astCache.set(expression, ast);
      return ast;
    } catch (error) {
      throw new Error(`表达式解析失败: ${expression}, 错误: ${error.message}`);
    }
  }

  /**
   * 提取表达式依赖
   */
  extractDependencies(ast: Expression): string[] {
    const tracker = new DependencyTrackerImpl();
    this.traverseASTForDependencies(ast, tracker);
    return tracker.getDependencies();
  }

  /**
   * 遍历AST提取依赖关系
   */
  private traverseASTForDependencies(node: Expression, tracker: DependencyTracker): void {
    switch (node.type) {
      case 'Identifier':
        // 单个标识符，如 price
        tracker.addDependency((node as Identifier).name);
        break;

      case 'MemberExpression':
        // 成员访问，如 ccuz8ypotl.modelValue.price
        const memberPath = this.getMemberExpressionPath(node as MemberExpression);
        if (memberPath) {
          tracker.addDependency(memberPath);
        }
        break;

      case 'BinaryExpression':
        const binaryNode = node as BinaryExpression;
        this.traverseASTForDependencies(binaryNode.left, tracker);
        this.traverseASTForDependencies(binaryNode.right, tracker);
        break;

      case 'UnaryExpression':
        const unaryNode = node as UnaryExpression;
        this.traverseASTForDependencies(unaryNode.argument, tracker);
        break;

      case 'ConditionalExpression':
        const conditionalNode = node as ConditionalExpression;
        this.traverseASTForDependencies(conditionalNode.test, tracker);
        this.traverseASTForDependencies(conditionalNode.consequent, tracker);
        this.traverseASTForDependencies(conditionalNode.alternate, tracker);
        break;

      case 'CallExpression':
        const callNode = node as CallExpression;
        this.traverseASTForDependencies(callNode.callee, tracker);
        callNode.arguments.forEach(arg => {
          this.traverseASTForDependencies(arg, tracker);
        });
        break;

      case 'ArrayExpression':
        const arrayNode = node as ArrayExpression;
        arrayNode.elements.forEach(element => {
          if (element) {
            this.traverseASTForDependencies(element, tracker);
          }
        });
        break;

      case 'Literal':
        // 字面量不产生依赖
        break;

      default:
        console.warn(`未处理的AST节点类型: ${node.type}`);
    }
  }

  /**
   * 获取成员表达式的完整路径
   */
  private getMemberExpressionPath(node: MemberExpression): string | null {
    const parts: string[] = [];
    let current: Expression = node;

    while (current) {
      if (current.type === 'MemberExpression') {
        const memberNode = current as MemberExpression;
        if (memberNode.property.type === 'Identifier') {
          parts.unshift((memberNode.property as Identifier).name);
        }
        current = memberNode.object;
      } else if (current.type === 'Identifier') {
        parts.unshift((current as Identifier).name);
        break;
      } else {
        // 复杂表达式，无法简单提取路径
        return null;
      }
    }

    return parts.join('.');
  }

  /**
   * 求值表达式
   */
  evaluate(expression: string | Expression, context: EnhancedExpressionContext = {}): ExpressionResult {
    try {
      // 1. 解析AST
      const ast = typeof expression === 'string' ? this.parse(expression) : expression;
      
      // 2. 提取依赖
      const dependencies = this.extractDependencies(ast);
      
      // 3. 检查缓存
      const cacheKey = this.generateCacheKey(ast, context, dependencies);
      const cachedValue = this.expressionCacheManager.get(cacheKey, dependencies);
      
      if (cachedValue !== null) {
        return {
          value: cachedValue,
          dependencies,
          ast
        };
      }

      // 4. 求值
      const value = this.evaluateNode(ast, context);
      
      // 5. 缓存结果
      this.expressionCacheManager.set(cacheKey, value, dependencies);

      return {
        value,
        dependencies,
        ast
      };

    } catch (error) {
      return {
        value: undefined,
        error: error as Error,
        dependencies: [],
        ast: { type: 'Literal', value: null, raw: 'null' } as Literal
      };
    }
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(ast: Expression, context: EnhancedExpressionContext, dependencies: string[]): string {
    const contextHash = context.currentComponent?.entityId || 'global';
    const astHash = JSON.stringify(ast); // 简单的AST序列化
    const depHash = dependencies.sort().join('|');
    
    return `${astHash}#${contextHash}#${depHash}`;
  }

  /**
   * 求值AST节点
   */
  private evaluateNode(node: Expression, context: EnhancedExpressionContext): any {
    switch (node.type) {
      case 'Literal':
        return (node as Literal).value;

      case 'Identifier':
        return this.resolveIdentifier((node as Identifier).name, context);

      case 'MemberExpression':
        return this.resolveMemberExpression(node as MemberExpression, context);

      case 'BinaryExpression':
        return this.evaluateBinaryExpression(node as BinaryExpression, context);

      case 'UnaryExpression':
        return this.evaluateUnaryExpression(node as UnaryExpression, context);

      case 'ConditionalExpression':
        return this.evaluateConditionalExpression(node as ConditionalExpression, context);

      case 'CallExpression':
        return this.evaluateCallExpression(node as CallExpression, context);

      case 'ArrayExpression':
        return this.evaluateArrayExpression(node as ArrayExpression, context);

      default:
        throw new Error(`不支持的表达式节点类型: ${node.type}`);
    }
  }

  /**
   * 解析标识符
   */
  private resolveIdentifier(name: string, context: EnhancedExpressionContext): any {
    // 1. 从安全作用域获取
    if (context.scope && context.scope.hasOwnProperty(name)) {
      return context.scope[name];
    }

    // 2. 从额外上下文获取
    if (context.extraContext && context.extraContext.hasOwnProperty(name)) {
      return context.extraContext[name];
    }

    // 3. 从全局状态获取
    const value = getValueByPath(name);
    if (value !== undefined) {
      return value;
    }

    // 4. 未找到
    return undefined;
  }

  /**
   * 解析成员表达式
   */
  private resolveMemberExpression(node: MemberExpression, context: EnhancedExpressionContext): any {
    const object = this.evaluateNode(node.object, context);
    
    if (object === undefined || object === null) {
      return undefined;
    }

    // 安全属性访问
    const propertyName = node.property.type === 'Identifier' 
      ? (node.property as Identifier).name 
      : this.evaluateNode(node.property, context);

    // 防止原型链攻击
    if (typeof propertyName === 'string' && this.isSafePropertyAccess(propertyName)) {
      return object[propertyName];
    }

    return undefined;
  }

  /**
   * 安全属性访问检查
   */
  private isSafePropertyAccess(property: string): boolean {
    const dangerousProperties = ['__proto__', 'constructor', 'prototype'];
    return !dangerousProperties.includes(property);
  }

  /**
   * 求值二元表达式
   */
  private evaluateBinaryExpression(node: BinaryExpression, context: EnhancedExpressionContext): any {
    const { operator, left, right } = node;

    // 特殊处理管道操作符
    if (operator === '|') {
      return this.evaluatePipeExpression(left, right, context);
    }

    const leftValue = this.evaluateNode(left, context);
    const rightValue = this.evaluateNode(right, context);

    switch (operator) {
      case '+': return leftValue + rightValue;
      case '-': return leftValue - rightValue;
      case '*': return leftValue * rightValue;
      case '/': return leftValue / rightValue;
      case '%': return leftValue % rightValue;
      case '**': return Math.pow(leftValue, rightValue);
      
      // 比较运算符
      case '==': return leftValue == rightValue;
      case '!=': return leftValue != rightValue;
      case '===': return leftValue === rightValue;
      case '!==': return leftValue !== rightValue;
      case '<': return leftValue < rightValue;
      case '<=': return leftValue <= rightValue;
      case '>': return leftValue > rightValue;
      case '>=': return leftValue >= rightValue;
      
      // 逻辑运算符
      case '&&': return leftValue && rightValue;
      case '||': return leftValue || rightValue;

      default:
        throw new Error(`不支持的二元操作符: ${operator}`);
    }
  }

  /**
   * 求值管道表达式
   */
  private evaluatePipeExpression(valueNode: Expression, pipeNode: Expression, context: EnhancedExpressionContext): any {
    const value = this.evaluateNode(valueNode, context);
    
    // 管道必须是函数调用或标识符
    if (pipeNode.type === 'Identifier') {
      const pipeName = (pipeNode as Identifier).name;
      return this.applyPipe(pipeName, value, {});
    } else if (pipeNode.type === 'CallExpression') {
      const callNode = pipeNode as CallExpression;
      if (callNode.callee.type === 'Identifier') {
        const pipeName = (callNode.callee as Identifier).name;
        const options = this.evaluateCallArguments(callNode.arguments, context);
        return this.applyPipe(pipeName, value, options[0] || {});
      }
    }

    throw new Error('管道右侧必须是函数名或函数调用');
  }

  /**
   * 应用管道函数
   */
  private applyPipe(pipeName: string, value: any, options: Record<string, any>): any {
    const pipeFunction = this.pipeRegistry.get(pipeName);
    if (!pipeFunction) {
      throw new Error(`未找到管道函数: ${pipeName}`);
    }

    return pipeFunction(value, options);
  }

  /**
   * 求值一元表达式
   */
  private evaluateUnaryExpression(node: UnaryExpression, context: EnhancedExpressionContext): any {
    const { operator, argument } = node;
    const value = this.evaluateNode(argument, context);

    switch (operator) {
      case '+': return +value;
      case '-': return -value;
      case '!': return !value;
      case 'typeof': return typeof value;
      default:
        throw new Error(`不支持的一元操作符: ${operator}`);
    }
  }

  /**
   * 求值条件表达式
   */
  private evaluateConditionalExpression(node: ConditionalExpression, context: EnhancedExpressionContext): any {
    const condition = this.evaluateNode(node.test, context);
    return condition 
      ? this.evaluateNode(node.consequent, context)
      : this.evaluateNode(node.alternate, context);
  }

  /**
   * 求值函数调用表达式
   */
  private evaluateCallExpression(node: CallExpression, context: EnhancedExpressionContext): any {
    const callee = node.callee;
    
    if (callee.type === 'Identifier') {
      const functionName = (callee as Identifier).name;
      const args = this.evaluateCallArguments(node.arguments, context);
      return this.callBuiltInFunction(functionName, args);
    }

    throw new Error('仅支持内置函数调用');
  }

  /**
   * 求值函数参数
   */
  private evaluateCallArguments(args: Expression[], context: EnhancedExpressionContext): any[] {
    return args.map(arg => this.evaluateNode(arg, context));
  }

  /**
   * 调用内置函数
   */
  private callBuiltInFunction(name: string, args: any[]): any {
    const builtInFunctions = {
      // 数学函数
      abs: Math.abs,
      ceil: Math.ceil,
      floor: Math.floor,
      round: Math.round,
      max: Math.max,
      min: Math.min,
      
      // 数组函数
      sum: (arr: number[]) => Array.isArray(arr) ? arr.reduce((a, b) => a + b, 0) : 0,
      avg: (arr: number[]) => Array.isArray(arr) && arr.length > 0 ? arr.reduce((a, b) => a + b, 0) / arr.length : 0,
      length: (value: any) => value?.length ?? 0,
      
      // 字符串函数
      substring: (str: string, start: number, end?: number) => String(str).substring(start, end),
      toLowerCase: (str: string) => String(str).toLowerCase(),
      toUpperCase: (str: string) => String(str).toUpperCase(),
      
      // 类型判断
      isEmpty: (value: any) => !value || (Array.isArray(value) && value.length === 0),
      isNumber: (value: any) => typeof value === 'number' && !isNaN(value),
      isString: (value: any) => typeof value === 'string'
    };

    const func = builtInFunctions[name as keyof typeof builtInFunctions];
    if (func) {
      return func.apply(null, args);
    }

    throw new Error(`未知的内置函数: ${name}`);
  }

  /**
   * 求值数组表达式
   */
  private evaluateArrayExpression(node: ArrayExpression, context: EnhancedExpressionContext): any[] {
    return node.elements.map(element => 
      element ? this.evaluateNode(element, context) : null
    );
  }

  /**
   * 注册自定义管道
   */
  registerPipe(name: string, fn: PipeFunction): void {
    this.pipeRegistry.register(name, fn);
  }

  /**
   * 应用管道链（用于处理JSON配置中的pipes数组）
   */
  applyPipes(value: any, pipesConfig: Array<{pipe: string, option: Record<string, any>}>): any {
    let result = value;

    for (const config of pipesConfig) {
      if (!config.pipe) continue;
      result = this.applyPipe(config.pipe, result, config.option || {});
    }

    return result;
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.astCache.clear();
    this.expressionCacheManager.clear();
  }
}

// 导出单例实例
export const enhancedExpressionEngine = new EnhancedExpressionEngine();

export default enhancedExpressionEngine;
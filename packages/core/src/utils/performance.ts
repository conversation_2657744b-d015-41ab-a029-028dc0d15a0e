/**
 * 性能优化工具集
 * 为低代码平台提供全面的性能优化机制
 * 
 * 基于Gemini架构建议的性能优化策略：
 * 1. 组件渲染优化 - 避免不必要的重渲染
 * 2. 状态更新优化 - 批量更新和去重
 * 3. 表达式计算缓存 - 避免重复计算
 * 4. 事件处理优化 - 节流防抖和队列管理
 * 5. 内存管理优化 - 自动清理和监控
 */

import { ComponentNode } from '../types/component.types';

// ========== 类型定义 ==========

export interface PerformanceMetrics {
  renderCount: number;
  updateCount: number;
  expressionCount: number;
  eventCount: number;
  memoryUsage: number;
  lastUpdate: number;
}

export interface OptimizationConfig {
  enableRenderOptimization: boolean;
  enableBatchUpdate: boolean;
  enableExpressionCache: boolean;
  enableEventThrottle: boolean;
  enableMemoryMonitor: boolean;
  maxCacheSize: number;
  throttleInterval: number;
  memoryCheckInterval: number;
}

// ========== 性能监控器 ==========

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, PerformanceMetrics> = new Map();
  private config: OptimizationConfig;
  private memoryTimer?: NodeJS.Timeout;

  private constructor(config: OptimizationConfig) {
    this.config = config;
    this.initializeMonitoring();
  }

  public static getInstance(config?: OptimizationConfig): PerformanceMonitor {
    if (!PerformanceMonitor.instance && config) {
      PerformanceMonitor.instance = new PerformanceMonitor(config);
    }
    return PerformanceMonitor.instance;
  }

  private initializeMonitoring(): void {
    if (this.config.enableMemoryMonitor) {
      this.memoryTimer = setInterval(() => {
        this.checkMemoryUsage();
      }, this.config.memoryCheckInterval);
    }
  }

  /**
   * 记录性能指标
   */
  public recordMetric(
    type: keyof PerformanceMetrics,
    componentId?: string
  ): void {
    const key = componentId || 'global';
    
    if (!this.metrics.has(key)) {
      this.metrics.set(key, {
        renderCount: 0,
        updateCount: 0,
        expressionCount: 0,
        eventCount: 0,
        memoryUsage: 0,
        lastUpdate: Date.now()
      });
    }

    const metrics = this.metrics.get(key)!;
    metrics[type]++;
    metrics.lastUpdate = Date.now();
  }

  /**
   * 获取性能报告
   */
  public getPerformanceReport(): Record<string, PerformanceMetrics> {
    const report: Record<string, PerformanceMetrics> = {};
    
    this.metrics.forEach((metrics, key) => {
      report[key] = { ...metrics };
    });

    return report;
  }

  /**
   * 检查内存使用情况
   */
  private checkMemoryUsage(): void {
    try {
      // 在浏览器环境中使用performance API
      if (typeof performance !== 'undefined' && 'memory' in performance) {
        const memory = (performance as any).memory;
        const memoryUsage = memory.usedJSHeapSize / 1024 / 1024; // MB
        
        this.metrics.forEach((metrics) => {
          metrics.memoryUsage = memoryUsage;
        });

        // 内存使用过高时触发警告
        if (memoryUsage > 100) { // 100MB警告线
          console.warn(`⚠️ 内存使用过高: ${memoryUsage.toFixed(2)}MB`);
        }
      }
    } catch (error) {
      console.warn('内存监控失败:', error);
    }
  }

  /**
   * 清理性能数据
   */
  public cleanup(): void {
    if (this.memoryTimer) {
      clearInterval(this.memoryTimer);
    }
    this.metrics.clear();
  }
}

// ========== 渲染优化 ==========

/**
 * 组件渲染优化器
 * 使用浅比较避免不必要的重渲染
 */
export class RenderOptimizer {
  private lastRenderProps: Map<string, any> = new Map();
  private lastRenderTime: Map<string, number> = new Map();

  /**
   * 检查组件是否需要重新渲染
   */
  public shouldComponentUpdate(
    componentId: string,
    currentProps: any,
    forceUpdate = false
  ): boolean {
    if (forceUpdate) {
      return true;
    }

    const lastProps = this.lastRenderProps.get(componentId);
    const lastTime = this.lastRenderTime.get(componentId);
    const now = Date.now();

    // 首次渲染
    if (!lastProps) {
      this.lastRenderProps.set(componentId, this.deepClone(currentProps));
      this.lastRenderTime.set(componentId, now);
      return true;
    }

    // 防止过于频繁的渲染 (16ms = 60fps)
    if (lastTime && (now - lastTime) < 16) {
      return false;
    }

    // 浅比较props
    const hasChanged = this.hasPropsChanged(lastProps, currentProps);
    
    if (hasChanged) {
      this.lastRenderProps.set(componentId, this.deepClone(currentProps));
      this.lastRenderTime.set(componentId, now);
      
      // 记录渲染指标
      const monitor = PerformanceMonitor.getInstance();
      monitor?.recordMetric('renderCount', componentId);
    }

    return hasChanged;
  }

  /**
   * 浅比较对象属性
   */
  private hasPropsChanged(prev: any, current: any): boolean {
    if (prev === current) {
      return false;
    }

    if (!prev || !current || typeof prev !== 'object' || typeof current !== 'object') {
      return prev !== current;
    }

    const prevKeys = Object.keys(prev);
    const currentKeys = Object.keys(current);

    if (prevKeys.length !== currentKeys.length) {
      return true;
    }

    for (const key of prevKeys) {
      if (prev[key] !== current[key]) {
        return true;
      }
    }

    return false;
  }

  /**
   * 深度克隆对象
   */
  private deepClone(obj: any): any {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (obj instanceof Date) {
      return new Date(obj);
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.deepClone(item));
    }

    const cloned: any = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = this.deepClone(obj[key]);
      }
    }

    return cloned;
  }

  /**
   * 清理渲染缓存
   */
  public cleanup(): void {
    this.lastRenderProps.clear();
    this.lastRenderTime.clear();
  }
}

// ========== 状态更新优化 ==========

/**
 * 批量状态更新器
 * 将多个状态更新合并为单次更新
 */
export class BatchUpdateManager {
  private updateQueue: Array<{
    componentId: string;
    updates: Record<string, any>;
    timestamp: number;
  }> = [];
  private isFlushPending = false;
  private flushTimer?: NodeJS.Timeout;

  /**
   * 添加状态更新到批量队列
   */
  public addUpdate(
    componentId: string,
    updates: Record<string, any>
  ): void {
    // 查找是否已有该组件的更新
    let existingUpdate = this.updateQueue.find(
      item => item.componentId === componentId
    );

    if (existingUpdate) {
      // 合并更新
      Object.assign(existingUpdate.updates, updates);
      existingUpdate.timestamp = Date.now();
    } else {
      // 添加新的更新
      this.updateQueue.push({
        componentId,
        updates: { ...updates },
        timestamp: Date.now()
      });
    }

    this.scheduleFlush();
  }

  /**
   * 调度批量更新刷新
   */
  private scheduleFlush(): void {
    if (this.isFlushPending) {
      return;
    }

    this.isFlushPending = true;

    // 使用 requestAnimationFrame 或 setTimeout 进行异步更新
    if (typeof requestAnimationFrame !== 'undefined') {
      requestAnimationFrame(() => this.flushUpdates());
    } else {
      this.flushTimer = setTimeout(() => this.flushUpdates(), 0);
    }
  }

  /**
   * 执行批量更新
   */
  private flushUpdates(): void {
    if (this.updateQueue.length === 0) {
      this.isFlushPending = false;
      return;
    }

    const updatesToProcess = [...this.updateQueue];
    this.updateQueue.length = 0;
    this.isFlushPending = false;

    try {
      // 按时间戳排序，确保更新顺序
      updatesToProcess.sort((a, b) => a.timestamp - b.timestamp);

      // 执行状态更新
      updatesToProcess.forEach(({ componentId, updates }) => {
        this.applyUpdates(componentId, updates);
        
        // 记录更新指标
        const monitor = PerformanceMonitor.getInstance();
        monitor?.recordMetric('updateCount', componentId);
      });

      console.log(`🚀 批量更新完成: ${updatesToProcess.length} 个组件`);
      
    } catch (error) {
      console.error('批量更新执行失败:', error);
    }
  }

  /**
   * 应用状态更新
   */
  private applyUpdates(componentId: string, updates: Record<string, any>): void {
    try {
      // 这里应该调用实际的状态更新函数
      // 由于需要访问rootStore，这个方法应该在初始化时注入更新函数
      if (this.updateHandler) {
        this.updateHandler(componentId, updates);
      }
    } catch (error) {
      console.error(`组件 ${componentId} 状态更新失败:`, error);
    }
  }

  private updateHandler?: (componentId: string, updates: Record<string, any>) => void;

  /**
   * 设置状态更新处理器
   */
  public setUpdateHandler(
    handler: (componentId: string, updates: Record<string, any>) => void
  ): void {
    this.updateHandler = handler;
  }

  /**
   * 强制刷新所有待处理的更新
   */
  public forceFlush(): void {
    if (this.flushTimer) {
      clearTimeout(this.flushTimer);
    }
    this.flushUpdates();
  }

  /**
   * 清理批量更新管理器
   */
  public cleanup(): void {
    if (this.flushTimer) {
      clearTimeout(this.flushTimer);
    }
    this.updateQueue.length = 0;
    this.isFlushPending = false;
  }
}

// ========== 事件处理优化 ==========

/**
 * 事件节流防抖管理器
 */
export class EventThrottleManager {
  private throttleMap: Map<string, {
    lastExecution: number;
    timeout?: NodeJS.Timeout;
    args?: any[];
  }> = new Map();

  /**
   * 节流函数
   * @param key 节流标识
   * @param fn 要执行的函数
   * @param interval 节流间隔(毫秒)
   */
  public throttle<T extends (...args: any[]) => any>(
    key: string,
    fn: T,
    interval: number
  ): (...args: Parameters<T>) => void {
    return (...args: Parameters<T>) => {
      const now = Date.now();
      const throttleData = this.throttleMap.get(key);

      if (!throttleData || (now - throttleData.lastExecution) >= interval) {
        // 可以执行
        this.throttleMap.set(key, { lastExecution: now });
        fn(...args);
        
        // 记录事件指标
        const monitor = PerformanceMonitor.getInstance();
        monitor?.recordMetric('eventCount');
      }
    };
  }

  /**
   * 防抖函数
   * @param key 防抖标识
   * @param fn 要执行的函数  
   * @param delay 防抖延迟(毫秒)
   */
  public debounce<T extends (...args: any[]) => any>(
    key: string,
    fn: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    return (...args: Parameters<T>) => {
      const throttleData = this.throttleMap.get(key);

      // 清除之前的定时器
      if (throttleData?.timeout) {
        clearTimeout(throttleData.timeout);
      }

      // 设置新的定时器
      const timeout = setTimeout(() => {
        fn(...args);
        this.throttleMap.delete(key);
        
        // 记录事件指标
        const monitor = PerformanceMonitor.getInstance();
        monitor?.recordMetric('eventCount');
      }, delay);

      this.throttleMap.set(key, {
        lastExecution: Date.now(),
        timeout,
        args
      });
    };
  }

  /**
   * 清理事件管理器
   */
  public cleanup(): void {
    this.throttleMap.forEach((data) => {
      if (data.timeout) {
        clearTimeout(data.timeout);
      }
    });
    this.throttleMap.clear();
  }
}

// ========== 表达式缓存优化 ==========

/**
 * 表达式计算缓存管理器
 */
export class ExpressionCacheManager {
  private cache: Map<string, {
    value: any;
    timestamp: number;
    dependencies: string[];
    hitCount: number;
  }> = new Map();
  
  private maxCacheSize: number;
  private cacheExpiry: number;

  constructor(maxCacheSize = 1000, cacheExpiry = 30000) {
    this.maxCacheSize = maxCacheSize;
    this.cacheExpiry = cacheExpiry;
  }

  /**
   * 获取缓存的表达式结果
   */
  public get(expression: string, dependencies: string[] = []): any | null {
    const cached = this.cache.get(expression);
    
    if (!cached) {
      return null;
    }

    // 检查是否过期
    if (Date.now() - cached.timestamp > this.cacheExpiry) {
      this.cache.delete(expression);
      return null;
    }

    // 检查依赖是否发生变化
    if (!this.areDependenciesValid(cached.dependencies, dependencies)) {
      this.cache.delete(expression);
      return null;
    }

    // 记录命中次数
    cached.hitCount++;
    
    return cached.value;
  }

  /**
   * 设置表达式结果到缓存
   */
  public set(
    expression: string, 
    value: any, 
    dependencies: string[] = []
  ): void {
    // 检查缓存大小限制
    if (this.cache.size >= this.maxCacheSize) {
      this.evictLeastUsed();
    }

    this.cache.set(expression, {
      value,
      timestamp: Date.now(),
      dependencies: [...dependencies],
      hitCount: 0
    });

    // 记录表达式指标
    const monitor = PerformanceMonitor.getInstance();
    monitor?.recordMetric('expressionCount');
  }

  /**
   * 检查依赖是否有效
   */
  private areDependenciesValid(
    cachedDeps: string[], 
    currentDeps: string[]
  ): boolean {
    if (cachedDeps.length !== currentDeps.length) {
      return false;
    }

    return cachedDeps.every((dep, index) => dep === currentDeps[index]);
  }

  /**
   * 清除最少使用的缓存项
   */
  private evictLeastUsed(): void {
    let leastUsedKey = '';
    let leastUsedHits = Infinity;

    this.cache.forEach((data, key) => {
      if (data.hitCount < leastUsedHits) {
        leastUsedHits = data.hitCount;
        leastUsedKey = key;
      }
    });

    if (leastUsedKey) {
      this.cache.delete(leastUsedKey);
    }
  }

  /**
   * 清除所有缓存
   */
  public clear(): void {
    this.cache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  public getStats(): {
    size: number;
    totalHits: number;
    averageHits: number;
  } {
    let totalHits = 0;
    this.cache.forEach((data) => {
      totalHits += data.hitCount;
    });

    return {
      size: this.cache.size,
      totalHits,
      averageHits: this.cache.size > 0 ? totalHits / this.cache.size : 0
    };
  }
}

// ========== 默认配置 ==========

export const DEFAULT_OPTIMIZATION_CONFIG: OptimizationConfig = {
  enableRenderOptimization: true,
  enableBatchUpdate: true,
  enableExpressionCache: true,
  enableEventThrottle: true,
  enableMemoryMonitor: true,
  maxCacheSize: 1000,
  throttleInterval: 100,
  memoryCheckInterval: 10000
};

// ========== 性能优化工具集导出 ==========

export const performanceTools = {
  monitor: (config = DEFAULT_OPTIMIZATION_CONFIG) => 
    PerformanceMonitor.getInstance(config),
  renderOptimizer: new RenderOptimizer(),
  batchUpdateManager: new BatchUpdateManager(),
  eventThrottleManager: new EventThrottleManager(),
  expressionCacheManager: new ExpressionCacheManager()
};

export default performanceTools;
import { rootStore, getValueByPath } from '../store/root-store';
import { performanceTools } from './performance';

/**
 * 表达式引擎 - 连接状态和配置的桥梁
 * 支持 {{expression}} 语法和复杂的路径访问
 * 基于Gemini架构建议实现
 */

// ========== 类型定义 ==========

export interface ExpressionContext {
  // 当前组件上下文 (可选)
  currentComponent?: any;
  // 额外的上下文数据 (可选)
  extraContext?: Record<string, any>;
  // 是否启用缓存
  enableCache?: boolean;
}

export interface ExpressionResult {
  value: any;
  error?: Error;
  dependencies?: string[]; // 依赖的状态路径
}

// 表达式缓存类型
interface ExpressionCache {
  [expression: string]: {
    result: any;
    timestamp: number;
    dependencies: string[];
  };
}

// ========== 性能优化集成 ==========

const { expressionCacheManager, monitor } = performanceTools;

// 保留原有缓存接口的兼容性
const expressionCache: ExpressionCache = {};
const CACHE_EXPIRE_TIME = 5000; // 5秒缓存过期

/**
 * 清理过期的表达式缓存 (向后兼容)
 */
function cleanExpiredCache() {
  const now = Date.now();
  Object.keys(expressionCache).forEach(key => {
    if (now - expressionCache[key].timestamp > CACHE_EXPIRE_TIME) {
      delete expressionCache[key];
    }
  });
}

// ========== 核心表达式引擎 ==========

/**
 * 解析和计算表达式 (性能优化版本)
 * @param expression 表达式字符串，支持 {{path}} 格式
 * @param context 表达式上下文
 * @returns 计算结果
 */
export function evaluateExpression(
  expression: any, 
  context: ExpressionContext = {}
): any {
  // 1. 非字符串直接返回
  if (typeof expression !== 'string') {
    return expression;
  }

  // 2. 不包含表达式语法直接返回
  if (!expression.includes('{{')) {
    return expression;
  }

  // 3. 提取依赖用于缓存键
  const dependencies = extractDependencies(expression);
  const cacheKey = generateCacheKey(expression, context, dependencies);

  // 4. 检查新的缓存管理器
  const cachedResult = expressionCacheManager.get(cacheKey, dependencies);
  if (cachedResult !== null) {
    // 缓存命中，记录性能指标
    // monitor?.recordMetric('expressionCount'); // 暂时移除 - 监控器无此方法
    return cachedResult;
  }

  // 5. 向后兼容：检查旧缓存 (可选)
  if (context.enableCache && expressionCache[expression]) {
    const cached = expressionCache[expression];
    if (Date.now() - cached.timestamp < CACHE_EXPIRE_TIME) {
      // 将旧缓存迁移到新缓存系统
      expressionCacheManager.set(cacheKey, cached.result, dependencies);
      return cached.result;
    }
  }

  try {
    // 6. 解析表达式
    const result = parseExpression(expression, context);
    
    // 7. 缓存结果到新的缓存管理器
    expressionCacheManager.set(cacheKey, result, dependencies);
    
    // 8. 向后兼容：同时缓存到旧系统 (可选)
    if (context.enableCache) {
      expressionCache[expression] = {
        result,
        timestamp: Date.now(),
        dependencies
      };
    }

    // 9. 记录性能指标
    // monitor?.recordMetric('expressionCount'); // 暂时移除 - 监控器无此方法

    return result;
  } catch (error) {
    console.error('表达式解析错误:', expression, error);
    return expression; // 解析失败时返回原始值
  }
}

/**
 * 生成表达式缓存键
 */
function generateCacheKey(
  expression: string,
  context: ExpressionContext,
  dependencies: string[]
): string {
  // 生成包含上下文信息的缓存键
  const contextHash = context.currentComponent?.entityId || 'global';
  const depHash = dependencies.sort().join('|');
  
  return `${expression}#${contextHash}#${depHash}`;
}

/**
 * 解析表达式主逻辑
 */
function parseExpression(expression: string, context: ExpressionContext): any {
  // 提取所有 {{...}} 表达式
  const expressionRegex = /\{\{([^}]+)\}\}/g;
  
  let result = expression;
  let match;
  
  // 收集所有匹配的表达式
  const matches: Array<{fullMatch: string, innerExpression: string}> = [];
  
  while ((match = expressionRegex.exec(expression)) !== null) {
    matches.push({
      fullMatch: match[0],
      innerExpression: match[1].trim()
    });
  }

  // 如果只有一个表达式且占据整个字符串，直接返回计算结果
  if (matches.length === 1 && expression === matches[0].fullMatch) {
    return evaluateSingleExpression(matches[0].innerExpression, context);
  }

  // 多个表达式或混合文本，进行字符串替换
  matches.forEach(({fullMatch, innerExpression}) => {
    const value = evaluateSingleExpression(innerExpression, context);
    const stringValue = convertToString(value);
    result = result.replace(fullMatch, stringValue);
  });

  return result;
}

/**
 * 计算单个表达式
 */
function evaluateSingleExpression(expr: string, context: ExpressionContext): any {
  // 去除首尾空格
  expr = expr.trim();
  
  // 1. 处理状态路径访问
  if (isStatePath(expr)) {
    return evaluateStatePath(expr, context);
  }

  // 2. 处理简单运算
  if (isSimpleCalculation(expr)) {
    return evaluateCalculation(expr, context);
  }

  // 3. 处理条件表达式 
  if (isConditionalExpression(expr)) {
    return evaluateConditional(expr, context);
  }

  // 4. 处理函数调用
  if (isFunctionCall(expr)) {
    return evaluateFunction(expr, context);
  }

  // 5. 默认作为状态路径处理
  return evaluateStatePath(expr, context);
}

// ========== 表达式类型判断 ==========

/**
 * 判断是否为状态路径 (如 components.form1.modelValue.username)
 */
function isStatePath(expr: string): boolean {
  return /^[a-zA-Z_$][a-zA-Z0-9_$.]*$/.test(expr);
}

/**
 * 判断是否为简单计算 (如 a + b, a * b)
 */
function isSimpleCalculation(expr: string): boolean {
  return /[\+\-\*\/\%]/.test(expr) && !expr.includes('(');
}

/**
 * 判断是否为条件表达式 (如 a === b ? c : d)
 */
function isConditionalExpression(expr: string): boolean {
  return /\?.*:/.test(expr);
}

/**
 * 判断是否为函数调用 (如 formatDate(), sum())
 */
function isFunctionCall(expr: string): boolean {
  return /^[a-zA-Z_$][a-zA-Z0-9_$]*\(.*\)$/.test(expr);
}

// ========== 表达式求值器 ==========

/**
 * 计算状态路径
 */
function evaluateStatePath(path: string, context: ExpressionContext): any {
  // 1. 尝试从根状态获取
  let value = getValueByPath(path);
  if (value !== undefined) {
    return value;
  }

  // 2. 尝试从当前组件上下文获取
  if (context.currentComponent) {
    value = getValueFromObject(context.currentComponent, path);
    if (value !== undefined) {
      return value;
    }
  }

  // 3. 尝试从额外上下文获取
  if (context.extraContext) {
    value = getValueFromObject(context.extraContext, path);
    if (value !== undefined) {
      return value;
    }
  }

  // 4. 返回undefined
  console.warn(`表达式路径未找到: ${path}`);
  return undefined;
}

/**
 * 计算简单数学运算
 */
function evaluateCalculation(expr: string, context: ExpressionContext): any {
  try {
    // 先解析表达式中的变量
    const resolvedExpr = expr.replace(/[a-zA-Z_$][a-zA-Z0-9_$.]*(?!\s*\()/g, (match) => {
      const value = evaluateStatePath(match.trim(), context);
      return value !== undefined ? String(value) : '0';
    });

    // 安全计算 (仅支持基础数学运算)
    const safeEval = new Function('return ' + resolvedExpr);
    return safeEval();
  } catch (error) {
    console.error('计算表达式错误:', expr, error);
    return 0;
  }
}

/**
 * 计算条件表达式 (三元运算符)
 */
function evaluateConditional(expr: string, context: ExpressionContext): any {
  const match = expr.match(/(.+?)\s*\?\s*(.+?)\s*:\s*(.+)/);
  if (!match) {
    return undefined;
  }

  const [, condition, trueValue, falseValue] = match;
  
  // 计算条件
  const conditionResult = evaluateCondition(condition.trim(), context);
  
  // 返回对应结果
  if (conditionResult) {
    return evaluateSingleExpression(trueValue.trim(), context);
  } else {
    return evaluateSingleExpression(falseValue.trim(), context);
  }
}

/**
 * 计算条件逻辑
 */
function evaluateCondition(condition: string, context: ExpressionContext): boolean {
  // 支持常见的比较操作符
  const operators = ['===', '!==', '==', '!=', '>=', '<=', '>', '<'];
  
  for (const op of operators) {
    if (condition.includes(op)) {
      const [left, right] = condition.split(op).map(s => s.trim());
      const leftValue = evaluateSingleExpression(left, context);
      const rightValue = evaluateSingleExpression(right, context);
      
      switch (op) {
        case '===': return leftValue === rightValue;
        case '!==': return leftValue !== rightValue;
        case '==': return leftValue == rightValue;
        case '!=': return leftValue != rightValue;
        case '>=': return leftValue >= rightValue;
        case '<=': return leftValue <= rightValue;
        case '>': return leftValue > rightValue;
        case '<': return leftValue < rightValue;
      }
    }
  }
  
  // 默认转为布尔值
  return Boolean(evaluateSingleExpression(condition, context));
}

/**
 * 函数调用求值 (扩展点)
 */
function evaluateFunction(expr: string, context: ExpressionContext): any {
  const match = expr.match(/^([a-zA-Z_$][a-zA-Z0-9_$]*)\((.*)\)$/);
  if (!match) {
    return undefined;
  }

  const [, funcName, argsStr] = match;
  const args = parseArguments(argsStr, context);

  // 内置函数
  switch (funcName) {
    case 'formatDate':
      return formatDate(args[0], args[1] || 'YYYY-MM-DD HH:mm:ss');
    case 'sum':
      return args.reduce((acc, val) => acc + (Number(val) || 0), 0);
    case 'length':
      return args[0] ? args[0].length : 0;
    case 'isEmpty':
      return !args[0] || args[0].length === 0;
    default:
      console.warn(`未知函数: ${funcName}`);
      return undefined;
  }
}

// ========== 工具函数 ==========

/**
 * 从对象中按路径获取值
 */
function getValueFromObject(obj: any, path: string): any {
  const pathArray = path.split('.');
  let current = obj;
  
  for (const key of pathArray) {
    if (current && typeof current === 'object' && key in current) {
      current = current[key];
    } else {
      return undefined;
    }
  }
  
  return current;
}

/**
 * 转换为字符串
 */
function convertToString(value: any): string {
  if (value === null || value === undefined) {
    return '';
  }
  if (typeof value === 'object') {
    return JSON.stringify(value);
  }
  return String(value);
}

/**
 * 解析函数参数
 */
function parseArguments(argsStr: string, context: ExpressionContext): any[] {
  if (!argsStr.trim()) {
    return [];
  }

  // 简单的参数分割 (不处理嵌套括号)
  return argsStr.split(',').map(arg => {
    const trimmedArg = arg.trim();
    // 如果是字符串字面量
    if ((trimmedArg.startsWith('"') && trimmedArg.endsWith('"')) ||
        (trimmedArg.startsWith("'") && trimmedArg.endsWith("'"))) {
      return trimmedArg.slice(1, -1);
    }
    // 否则作为表达式计算
    return evaluateSingleExpression(trimmedArg, context);
  });
}

/**
 * 提取表达式依赖的状态路径
 */
function extractDependencies(expression: string): string[] {
  const dependencies: string[] = [];
  const regex = /\{\{([^}]+)\}\}/g;
  let match;
  
  while ((match = regex.exec(expression)) !== null) {
    const innerExpr = match[1].trim();
    // 提取其中的状态路径
    const pathMatches = innerExpr.match(/[a-zA-Z_$][a-zA-Z0-9_$.]*(?!\s*\()/g);
    if (pathMatches) {
      dependencies.push(...pathMatches);
    }
  }
  
  return [...new Set(dependencies)]; // 去重
}

/**
 * 内置日期格式化函数
 */
function formatDate(date: any, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!date) return '';
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}

// ========== 高级功能 ==========

/**
 * 批量计算多个表达式
 */
export function evaluateExpressions(
  expressions: Record<string, any>,
  context: ExpressionContext = {}
): Record<string, any> {
  const results: Record<string, any> = {};
  
  Object.keys(expressions).forEach(key => {
    results[key] = evaluateExpression(expressions[key], context);
  });
  
  return results;
}

/**
 * 表达式模板渲染 (类似模板引擎)
 */
export function renderTemplate(
  template: string,
  data: Record<string, any>
): string {
  return evaluateExpression(template, { extraContext: data });
}

/**
 * 清理表达式缓存
 */
export function clearExpressionCache() {
  Object.keys(expressionCache).forEach(key => {
    delete expressionCache[key];
  });
  console.log('表达式缓存已清理');
}

// 定期清理过期缓存
setInterval(cleanExpiredCache, 30000); // 每30秒清理一次

export default evaluateExpression;
/**
 * 性能监控和报告工具
 * 为低代码平台提供实时性能监控、分析和优化建议
 * 
 * 功能特性：
 * 1. 实时性能指标收集
 * 2. 性能瓶颈自动检测
 * 3. 优化建议生成
 * 4. 性能报告导出
 * 5. 内存泄漏检测
 */

import { optimization, getPerformanceReport } from '../store/root-store';
import { performanceTools } from './performance';

// ========== 类型定义 ==========

export interface PerformanceReport {
  timestamp: number;
  duration: number;
  summary: {
    totalRenders: number;
    totalUpdates: number;
    totalExpressions: number;
    totalEvents: number;
    averageMemory: number;
    componentsCount: number;
  };
  components: ComponentPerformance[];
  bottlenecks: PerformanceBottleneck[];
  recommendations: OptimizationRecommendation[];
  cacheStats: {
    expressionCache: {
      size: number;
      hitRate: number;
      totalHits: number;
    };
    renderCache: {
      skippedRenders: number;
      optimizationRate: number;
    };
  };
}

export interface ComponentPerformance {
  entityId: string;
  renderCount: number;
  updateCount: number;
  eventCount: number;
  memoryUsage: number;
  avgRenderTime?: number;
  score: 'excellent' | 'good' | 'fair' | 'poor';
}

export interface PerformanceBottleneck {
  type: 'render' | 'update' | 'expression' | 'event' | 'memory';
  severity: 'high' | 'medium' | 'low';
  componentId?: string;
  description: string;
  impact: string;
  suggestion: string;
}

export interface OptimizationRecommendation {
  category: 'rendering' | 'state' | 'events' | 'memory' | 'caching';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  implementation: string;
  expectedImprovement: string;
}

// ========== 性能监控器 ==========

export class PerformanceMonitoringService {
  private static instance: PerformanceMonitoringService;
  private startTime: number = Date.now();
  private reportHistory: PerformanceReport[] = [];
  private monitoringInterval?: NodeJS.Timeout;
  private isMonitoring = false;

  private constructor() {}

  public static getInstance(): PerformanceMonitoringService {
    if (!PerformanceMonitoringService.instance) {
      PerformanceMonitoringService.instance = new PerformanceMonitoringService();
    }
    return PerformanceMonitoringService.instance;
  }

  /**
   * 开始性能监控
   */
  public startMonitoring(intervalMs: number = 5000): void {
    if (this.isMonitoring) {
      console.warn('性能监控已在运行中');
      return;
    }

    console.log('🚀 开始性能监控...');
    this.isMonitoring = true;
    this.startTime = Date.now();

    this.monitoringInterval = setInterval(() => {
      this.collectAndAnalyzeMetrics();
    }, intervalMs);
  }

  /**
   * 停止性能监控
   */
  public stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }
    
    this.isMonitoring = false;
    console.log('⏹️ 性能监控已停止');
  }

  /**
   * 收集和分析性能指标
   */
  private collectAndAnalyzeMetrics(): void {
    try {
      const report = this.generatePerformanceReport();
      this.reportHistory.push(report);
      
      // 保留最近20个报告
      if (this.reportHistory.length > 20) {
        this.reportHistory = this.reportHistory.slice(-20);
      }

      // 检测性能问题
      const criticalIssues = report.bottlenecks.filter(b => b.severity === 'high');
      if (criticalIssues.length > 0) {
        console.warn('⚠️ 检测到严重性能问题:', criticalIssues);
        this.handlePerformanceAlert(criticalIssues);
      }

      // 输出性能摘要
      this.logPerformanceSummary(report);
      
    } catch (error) {
      console.error('性能监控收集失败:', error);
    }
  }

  /**
   * 生成性能报告
   */
  public generatePerformanceReport(): PerformanceReport {
    const now = Date.now();
    const duration = now - this.startTime;
    
    // 获取原始性能数据
    const rawMetrics = getPerformanceReport();
    
    // 分析组件性能
    const components = this.analyzeComponentPerformance(rawMetrics);
    
    // 检测性能瓶颈
    const bottlenecks = this.detectBottlenecks(components, rawMetrics);
    
    // 生成优化建议
    const recommendations = this.generateRecommendations(bottlenecks, components);
    
    // 获取缓存统计
    const cacheStats = this.getCacheStatistics();
    
    // 计算总体摘要
    const summary = this.calculateSummary(components);

    return {
      timestamp: now,
      duration,
      summary,
      components,
      bottlenecks,
      recommendations,
      cacheStats
    };
  }

  /**
   * 分析组件性能
   */
  private analyzeComponentPerformance(rawMetrics: any): ComponentPerformance[] {
    const components: ComponentPerformance[] = [];

    Object.entries(rawMetrics).forEach(([entityId, metrics]) => {
      if (entityId === 'global') return;
      
      const m = metrics as any;
      const score = this.calculatePerformanceScore(m);

      components.push({
        entityId,
        renderCount: m.renderCount || 0,
        updateCount: m.updateCount || 0,
        eventCount: m.eventCount || 0,
        memoryUsage: m.memoryUsage || 0,
        score
      });
    });

    return components.sort((a, b) => b.renderCount - a.renderCount);
  }

  /**
   * 计算性能评分
   */
  private calculatePerformanceScore(metrics: any): ComponentPerformance['score'] {
    const renderCount = metrics.renderCount || 0;
    const updateCount = metrics.updateCount || 0;
    const eventCount = metrics.eventCount || 0;

    // 简化的评分算法
    const totalActivity = renderCount + updateCount + eventCount;
    
    if (totalActivity < 10) return 'excellent';
    if (totalActivity < 50) return 'good';
    if (totalActivity < 200) return 'fair';
    return 'poor';
  }

  /**
   * 检测性能瓶颈
   */
  private detectBottlenecks(
    components: ComponentPerformance[], 
    rawMetrics: any
  ): PerformanceBottleneck[] {
    const bottlenecks: PerformanceBottleneck[] = [];

    // 检测频繁渲染的组件
    const highRenderComponents = components.filter(c => c.renderCount > 100);
    highRenderComponents.forEach(component => {
      bottlenecks.push({
        type: 'render',
        severity: 'high',
        componentId: component.entityId,
        description: `组件 ${component.entityId} 渲染次数过多 (${component.renderCount} 次)`,
        impact: '可能导致页面卡顿和性能下降',
        suggestion: '考虑使用 React.memo 或减少不必要的状态更新'
      });
    });

    // 检测频繁更新的组件
    const highUpdateComponents = components.filter(c => c.updateCount > 200);
    highUpdateComponents.forEach(component => {
      bottlenecks.push({
        type: 'update',
        severity: 'medium',
        componentId: component.entityId,
        description: `组件 ${component.entityId} 状态更新过于频繁 (${component.updateCount} 次)`,
        impact: '增加系统负载和内存使用',
        suggestion: '启用批量更新或减少状态更新频率'
      });
    });

    // 检测内存使用
    const globalMetrics = rawMetrics.global;
    if (globalMetrics && globalMetrics.memoryUsage > 50) {
      bottlenecks.push({
        type: 'memory',
        severity: 'high',
        description: `内存使用过高 (${globalMetrics.memoryUsage.toFixed(2)} MB)`,
        impact: '可能导致页面崩溃或性能严重下降',
        suggestion: '检查是否存在内存泄漏，清理不必要的缓存'
      });
    }

    return bottlenecks;
  }

  /**
   * 生成优化建议
   */
  private generateRecommendations(
    bottlenecks: PerformanceBottleneck[],
    components: ComponentPerformance[]
  ): OptimizationRecommendation[] {
    const recommendations: OptimizationRecommendation[] = [];

    // 渲染优化建议
    if (bottlenecks.some(b => b.type === 'render')) {
      recommendations.push({
        category: 'rendering',
        priority: 'high',
        title: '启用渲染优化',
        description: '检测到组件频繁重渲染，建议启用渲染优化机制',
        implementation: '使用 shouldComponentUpdate 或 React.memo 来避免不必要的重渲染',
        expectedImprovement: '可减少 30-50% 的渲染次数'
      });
    }

    // 状态更新优化建议
    if (bottlenecks.some(b => b.type === 'update')) {
      recommendations.push({
        category: 'state',
        priority: 'medium',
        title: '使用批量状态更新',
        description: '组件状态更新频繁，建议使用批量更新机制',
        implementation: '调用 optimizedPageActions.updateComponentProps() 进行批量更新',
        expectedImprovement: '可减少 40-60% 的状态更新次数'
      });
    }

    // 缓存优化建议
    const cacheStats = this.getCacheStatistics();
    if (cacheStats.expressionCache.hitRate < 0.5) {
      recommendations.push({
        category: 'caching',
        priority: 'medium',
        title: '优化表达式缓存',
        description: '表达式缓存命中率较低，建议优化缓存策略',
        implementation: '增加缓存大小或调整缓存失效策略',
        expectedImprovement: '可提升 20-40% 的表达式计算性能'
      });
    }

    // 内存优化建议
    if (bottlenecks.some(b => b.type === 'memory')) {
      recommendations.push({
        category: 'memory',
        priority: 'high',
        title: '内存优化',
        description: '检测到内存使用过高，需要进行内存优化',
        implementation: '清理不必要的缓存，避免内存泄漏，使用懒加载',
        expectedImprovement: '可减少 30-50% 的内存占用'
      });
    }

    return recommendations;
  }

  /**
   * 获取缓存统计信息
   */
  private getCacheStatistics() {
    const expressionStats = optimization.expressionCacheManager.getStats();
    
    return {
      expressionCache: {
        size: expressionStats.size,
        hitRate: expressionStats.averageHits > 0 ? 
          Math.min(expressionStats.averageHits / 10, 1) : 0,
        totalHits: expressionStats.totalHits
      },
      renderCache: {
        skippedRenders: 0, // 这需要从渲染优化器获取
        optimizationRate: 0.3 // 估计值
      }
    };
  }

  /**
   * 计算性能摘要
   */
  private calculateSummary(components: ComponentPerformance[]) {
    return {
      totalRenders: components.reduce((sum, c) => sum + c.renderCount, 0),
      totalUpdates: components.reduce((sum, c) => sum + c.updateCount, 0),
      totalExpressions: 0, // 从表达式缓存获取
      totalEvents: components.reduce((sum, c) => sum + c.eventCount, 0),
      averageMemory: components.length > 0 ? 
        components.reduce((sum, c) => sum + c.memoryUsage, 0) / components.length : 0,
      componentsCount: components.length
    };
  }

  /**
   * 处理性能警报
   */
  private handlePerformanceAlert(criticalIssues: PerformanceBottleneck[]): void {
    // 在实际应用中，这里可以发送告警通知
    console.error('🚨 严重性能问题检测:', {
      issueCount: criticalIssues.length,
      issues: criticalIssues.map(issue => ({
        type: issue.type,
        description: issue.description,
        component: issue.componentId
      }))
    });
  }

  /**
   * 输出性能摘要日志
   */
  private logPerformanceSummary(report: PerformanceReport): void {
    console.log('📊 性能监控摘要:', {
      duration: `${Math.round(report.duration / 1000)}s`,
      components: report.summary.componentsCount,
      renders: report.summary.totalRenders,
      updates: report.summary.totalUpdates,
      events: report.summary.totalEvents,
      memory: `${report.summary.averageMemory.toFixed(2)}MB`,
      bottlenecks: report.bottlenecks.length,
      recommendations: report.recommendations.length
    });
  }

  /**
   * 获取历史报告
   */
  public getReportHistory(): PerformanceReport[] {
    return [...this.reportHistory];
  }

  /**
   * 获取最新报告
   */
  public getLatestReport(): PerformanceReport | null {
    return this.reportHistory.length > 0 
      ? this.reportHistory[this.reportHistory.length - 1] 
      : null;
  }

  /**
   * 导出性能报告
   */
  public exportReport(format: 'json' | 'csv' = 'json'): string {
    const report = this.getLatestReport();
    if (!report) {
      throw new Error('没有可用的性能报告');
    }

    if (format === 'json') {
      return JSON.stringify(report, null, 2);
    } else {
      return this.convertToCSV(report);
    }
  }

  /**
   * 转换为CSV格式
   */
  private convertToCSV(report: PerformanceReport): string {
    const headers = ['Component ID', 'Renders', 'Updates', 'Events', 'Memory (MB)', 'Score'];
    const rows = report.components.map(c => [
      c.entityId,
      c.renderCount,
      c.updateCount, 
      c.eventCount,
      c.memoryUsage.toFixed(2),
      c.score
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  /**
   * 清理监控数据
   */
  public cleanup(): void {
    this.stopMonitoring();
    this.reportHistory.length = 0;
  }
}

// ========== 便捷函数导出 ==========

export const performanceMonitor = PerformanceMonitoringService.getInstance();

/**
 * 快速启动性能监控
 */
export function startPerformanceMonitoring(intervalMs = 5000) {
  performanceMonitor.startMonitoring(intervalMs);
}

/**
 * 获取性能报告
 */
export function getPerformanceAnalysis() {
  return performanceMonitor.generatePerformanceReport();
}

/**
 * 获取性能优化建议
 */
export function getOptimizationSuggestions() {
  const report = performanceMonitor.getLatestReport();
  return report ? report.recommendations : [];
}

export default performanceMonitor;
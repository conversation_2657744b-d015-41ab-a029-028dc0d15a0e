import { enhancedExpressionEngine, EnhancedExpressionContext, ExpressionResult } from './enhanced-expression-engine';
import { rootStore } from '../store/root-store';

/**
 * Formula表达式处理器
 * 专门处理lyy-formula组件的表达式计算和依赖追踪
 */

export interface FormulaConfig {
  field: string;
  label: string;
  exp: string;
  type: 'text' | 'number' | 'currency';
  pipes?: Array<{
    pipe: string;
    option: Record<string, any>;
  }>;
  formId?: string;
  placeholder?: string;
  defaultValue?: any;
}

export interface FormulaResult {
  value: any;
  displayValue: string;
  dependencies: string[];
  error?: Error;
  isValid: boolean;
}

export interface FormulaSubscription {
  formulaId: string;
  dependencies: string[];
  callback: (result: FormulaResult) => void;
}

/**
 * Formula表达式处理器类
 */
export class FormulaExpressionProcessor {
  private subscriptions = new Map<string, FormulaSubscription>();
  private dependencyMap = new Map<string, Set<string>>(); // 依赖路径 -> 公式ID集合

  /**
   * 计算formula表达式
   */
  calculate(formulaId: string, config: FormulaConfig, context: EnhancedExpressionContext = {}): FormulaResult {
    try {
      // 1. 基础验证
      if (!config.exp?.trim()) {
        return this.createEmptyResult(config);
      }

      // 2. 构建表达式上下文
      const expressionContext = this.buildContext(config, context);

      // 3. 计算主表达式
      const result = enhancedExpressionEngine.evaluate(config.exp, expressionContext);
      
      if (result.error) {
        console.error(`Formula计算错误 [${formulaId}]:`, result.error);
        return {
          value: config.defaultValue,
          displayValue: this.formatDisplayValue(config.defaultValue, config.type),
          dependencies: result.dependencies,
          error: result.error,
          isValid: false
        };
      }

      // 4. 应用管道处理
      let finalValue = result.value;
      if (config.pipes && config.pipes.length > 0) {
        finalValue = enhancedExpressionEngine.applyPipes(result.value, config.pipes);
      }

      // 5. 格式化显示值
      const displayValue = this.formatDisplayValue(finalValue, config.type);

      return {
        value: finalValue,
        displayValue,
        dependencies: result.dependencies,
        isValid: true
      };

    } catch (error) {
      console.error(`Formula处理异常 [${formulaId}]:`, error);
      return {
        value: config.defaultValue,
        displayValue: this.formatDisplayValue(config.defaultValue, config.type),
        dependencies: [],
        error: error as Error,
        isValid: false
      };
    }
  }

  /**
   * 创建空结果
   */
  private createEmptyResult(config: FormulaConfig): FormulaResult {
    return {
      value: config.defaultValue ?? '',
      displayValue: this.formatDisplayValue(config.defaultValue ?? '', config.type),
      dependencies: [],
      isValid: true
    };
  }

  /**
   * 构建表达式执行上下文
   */
  private buildContext(config: FormulaConfig, userContext: EnhancedExpressionContext): EnhancedExpressionContext {
    return {
      ...userContext,
      scope: {
        // 注入常用的工具函数
        Math,
        Date,
        // 表单相关的便捷访问
        ...(userContext.scope || {}),
        ...(userContext.extraContext || {})
      },
      enableCache: true
    };
  }

  /**
   * 格式化显示值
   */
  private formatDisplayValue(value: any, type: FormulaConfig['type']): string {
    if (value === null || value === undefined) {
      return '';
    }

    switch (type) {
      case 'number':
        const num = Number(value);
        return isNaN(num) ? String(value) : num.toString();
      
      case 'currency':
        const currencyNum = Number(value);
        if (isNaN(currencyNum)) return String(value);
        return `¥${currencyNum.toFixed(2)}`;
      
      case 'text':
      default:
        return String(value);
    }
  }

  /**
   * 订阅formula的依赖变化
   */
  subscribe(formulaId: string, config: FormulaConfig, callback: (result: FormulaResult) => void, context?: EnhancedExpressionContext): () => void {
    // 1. 先计算一次获取依赖
    const initialResult = this.calculate(formulaId, config, context);
    
    // 2. 记录订阅信息
    const subscription: FormulaSubscription = {
      formulaId,
      dependencies: initialResult.dependencies,
      callback
    };

    this.subscriptions.set(formulaId, subscription);

    // 3. 建立依赖关系映射
    initialResult.dependencies.forEach(dep => {
      if (!this.dependencyMap.has(dep)) {
        this.dependencyMap.set(dep, new Set());
      }
      this.dependencyMap.get(dep)!.add(formulaId);
    });

    // 4. 立即触发一次回调
    callback(initialResult);

    // 5. 返回取消订阅函数
    return () => this.unsubscribe(formulaId);
  }

  /**
   * 取消订阅
   */
  unsubscribe(formulaId: string): void {
    const subscription = this.subscriptions.get(formulaId);
    if (!subscription) return;

    // 清理依赖映射
    subscription.dependencies.forEach(dep => {
      const formulaSet = this.dependencyMap.get(dep);
      if (formulaSet) {
        formulaSet.delete(formulaId);
        if (formulaSet.size === 0) {
          this.dependencyMap.delete(dep);
        }
      }
    });

    // 删除订阅
    this.subscriptions.delete(formulaId);
  }

  /**
   * 通知依赖变化（由状态管理系统调用）
   */
  notifyDependencyChange(dependencyPath: string): void {
    const affectedFormulas = this.dependencyMap.get(dependencyPath);
    if (!affectedFormulas) return;

    // 重新计算所有受影响的formula
    affectedFormulas.forEach(formulaId => {
      const subscription = this.subscriptions.get(formulaId);
      if (subscription) {
        // 这里需要重新获取config，实际使用时需要从组件实例中获取
        // 现在作为接口预留
        this.recalculateFormula(formulaId, subscription);
      }
    });
  }

  /**
   * 重新计算formula（预留接口）
   */
  private recalculateFormula(formulaId: string, subscription: FormulaSubscription): void {
    // TODO: 实际实现需要从组件注册表中获取最新的config
    console.log(`Formula ${formulaId} 需要重新计算，依赖路径变化:`, subscription.dependencies);
    
    // 临时实现：直接调用回调通知需要更新
    // 实际应该重新计算后再调用
    // subscription.callback(newResult);
  }

  /**
   * 批量计算多个formula
   */
  calculateBatch(formulas: Array<{
    formulaId: string;
    config: FormulaConfig;
    context?: EnhancedExpressionContext;
  }>): Map<string, FormulaResult> {
    const results = new Map<string, FormulaResult>();

    formulas.forEach(({ formulaId, config, context }) => {
      const result = this.calculate(formulaId, config, context);
      results.set(formulaId, result);
    });

    return results;
  }

  /**
   * 获取当前所有订阅的formula
   */
  getActiveSubscriptions(): string[] {
    return Array.from(this.subscriptions.keys());
  }

  /**
   * 获取依赖关系图
   */
  getDependencyGraph(): Record<string, string[]> {
    const graph: Record<string, string[]> = {};
    
    this.dependencyMap.forEach((formulaIds, dependency) => {
      graph[dependency] = Array.from(formulaIds);
    });

    return graph;
  }

  /**
   * 清理所有订阅
   */
  clearAll(): void {
    this.subscriptions.clear();
    this.dependencyMap.clear();
  }

  /**
   * 验证表达式语法
   */
  validateExpression(expression: string): { isValid: boolean; error?: string } {
    try {
      enhancedExpressionEngine.parse(expression);
      return { isValid: true };
    } catch (error) {
      return { 
        isValid: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  /**
   * 预分析表达式依赖（不执行求值）
   */
  analyzeExpression(expression: string): { dependencies: string[]; error?: string } {
    try {
      const ast = enhancedExpressionEngine.parse(expression);
      const dependencies = enhancedExpressionEngine.extractDependencies(ast);
      return { dependencies };
    } catch (error) {
      return { 
        dependencies: [], 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }
}

// 导出单例实例
export const formulaProcessor = new FormulaExpressionProcessor();

export default formulaProcessor;
import {ComponentAction, ComponentNode} from '../types/component.types';

interface IGenerateHandlerProps {
  action: ComponentAction,
  handlerName: string,
  node: ComponentNode,
}

export class EventHandlerGenerator {
  generateHandlerName(entityId: string, event: string): string {
    return `handle_${entityId}_${event}`;
  }

  generateHandlerCode(props: IGenerateHandlerProps): string {
    const { action, handlerName, node } = props

    switch (action.action) {
      case 'request':
        return this.generateRequestHandler(action, handlerName, node);
      default:
        return this.generateDefaultHandler(action, handlerName, node);
    }
  }

  private generateRequestHandler(action: ComponentAction, handlerName: string, node: ComponentNode): string {
    const { url, method, responseDataKey } = action.option;
    const payload = this.generateRequestPayload(action, node.entityId);

    return `
      const ${handlerName} = async () => {
        try {
          const response = await Taro.request({
            url: '${url}',
            method: '${method.toLocaleUpperCase()}',
            data: ${payload},
            header: {
              'Content-Type': 'application/json'
            }
          });
          ${responseDataKey ? `${node.entityId}.${responseDataKey} = response.data;` : ''}
          console.log('Request success:', response.data);
        } catch (error) {
          console.error('Request failed:', error);
        }
      };
    `;
  }

  private generateRequestPayload(action: ComponentAction, entityId: string): string {
    if (action.option.payloads.type === 'dynamic') {
      return `store.getComponentProps('${entityId}')`;
    }
    return '{}';
  }

  private generateDefaultHandler(action: ComponentAction, handlerName: string, node: ComponentNode): string {
    return `
      const ${handlerName} = () => {
        console.log('${action.action} event triggered for ${node.entityId}');
        // Add custom handler logic here
      };
    `;
  }
}
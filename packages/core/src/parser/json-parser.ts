import { ComponentNode, ParsedComponentTree } from '../types/component.types'
import { pageActions } from "../store"
import { EventHandlerGenerator } from './event-handler-generator'

export class JsonParser {
  private eventHandlerGenerator = new EventHandlerGenerator();

  parse(jsonData: ComponentNode): ParsedComponentTree {
    pageActions.registerComponent(jsonData);
    return this.parseNode(jsonData);
  }

  parseToComponentTrees(jsonConfig: any): ParsedComponentTree[] {
    const trees: ParsedComponentTree[] = [];
    if (jsonConfig.pages && Array.isArray(jsonConfig.pages)) {
      jsonConfig.pages.forEach(page => {
        trees.push(this.parse(page));
      });
    }
    return trees;
  }

  private parseNode(node: ComponentNode): ParsedComponentTree {
    // 处理子节点
    const children: ParsedComponentTree[] = [];
    if (node.childrens && node.childrens.length > 0) {
      node.childrens.forEach(child => {
        pageActions.registerComponent(child);
        children.push(this.parseNode(child));
      });
    }

    // 处理事件
    const eventHandlers: Record<string, string> = {};
    if (node.actions && node.actions.length > 0) {
      node.actions.forEach(action => {
        const handlerName = this.eventHandlerGenerator.generateHandlerName(
          node.entityId,
          action.event
        );
        eventHandlers[action.event] = handlerName;
      });
    }

    return {
      ...node,
      entityId: node.entityId,
      compName: node.compName!,
      prop: node.prop,
      eventHandlers: eventHandlers,
      children,
      actions: node.actions || []
    };
  }
}

// 兼容导出
export { JsonParser as JSONParser };
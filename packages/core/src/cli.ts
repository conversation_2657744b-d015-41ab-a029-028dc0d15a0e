#!/usr/bin/env node

import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { dirname } from 'path';
import {JsonParser, ASTCodeGenerator} from "./build";

// 获取命令行参数
const args = process.argv.slice(2);
const inputPath = args[0];
const outputPath = args[1] || './output/page.tsx';

// 移除旧逻辑，统一使用AST生成器

if (!inputPath) {
  console.error('请提供输入JSON文件路径');
  process.exit(1);
}

if (!existsSync(inputPath)) {
  console.error(`输入文件不存在: ${inputPath}`);
  process.exit(1);
}

(async () => {
  try {
    console.log(`🚀 开始处理: ${inputPath}`);
    console.log(`📝 使用生成器: AST代码生成器`);
    
    // 读取JSON文件
    const jsonContent = readFileSync(inputPath, 'utf-8');
    const jsonData = JSON.parse(jsonContent);
    
    // 解析JSON
    const parser = new JsonParser()
    const config = parser.parse(jsonData);
    const trees = Array.isArray(config) ? config : [config];

    // 生成代码 (仅使用AST生成器)
    console.log('🔧 使用AST代码生成器...');
    const astGenerator = new ASTCodeGenerator({
      enablePerformanceOptimization: true,
      generateTypeDefinitions: true,
      enableExpressionEngine: true,
      enableEventSystem: true,
      targetFramework: 'taro'
    });
    const result = await astGenerator.generatePageCode(trees);
    const generatedCode = result.code;
    console.log(`✅ AST生成完成，代码长度: ${generatedCode.length} 字符`);
    
    // 确保输出目录存在
    const outputDir = dirname(outputPath);
    if (!existsSync(outputDir)) {
      mkdirSync(outputDir, { recursive: true });
    }
    
    // 写入文件
    writeFileSync(outputPath, generatedCode, 'utf-8');
    
    console.log(`代码生成成功: ${outputPath}`);
    
    // 显式退出进程
    process.exit(0);
  } catch (error) {
    console.error('处理过程中出错:', error);
    process.exit(1);
  }
})();
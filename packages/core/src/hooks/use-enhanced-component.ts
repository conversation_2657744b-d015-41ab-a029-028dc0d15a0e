import { useSyncExternalStore, useMemo, useCallback } from 'react';
import { useSnapshot } from 'valtio';
import { rootStore, pageActions, optimization } from '../store/root-store';
import { eventDispatcher, ActionConfig } from '../events/event-dispatcher';
import { ComponentNode } from '../types/component.types';

/**
 * 增强的组件Hook - 基于三层状态架构
 * 支持响应式状态、事件处理、表达式计算
 * 替代原有的useReactiveComponent
 */

export interface UseEnhancedComponentResult {
  // 组件数据 (响应式)
  component: ComponentNode;
  // 组件状态快照 (用于渲染) - 只读
  snapshot: Readonly<ComponentNode>;
  // 事件处理函数 (支持节流优化)
  executeAction: (actions: ActionConfig | ActionConfig[], triggerEvent?: string) => Promise<void>;
  // 更新组件属性 (支持批量优化)
  updateProp: (propPath: string, value: any, immediate?: boolean) => void;
  // 批量更新多个属性
  updateProps: (updates: Record<string, any>) => void;
  // 检查组件是否存在
  exists: boolean;
  // 渲染优化标识
  shouldRender: boolean;
}

/**
 * 使用增强组件Hook (性能优化版本)
 * @param entityId 组件实体ID
 * @returns 组件数据和操作函数
 */
export function useEnhancedComponent(entityId: string): UseEnhancedComponentResult {
  // 1. 获取组件的响应式引用 (可写)
  const component = rootStore.page.components[entityId];
  
  // 2. 使用Valtio的useSnapshot获取只读快照 (用于渲染)
  const rootSnapshot = useSnapshot(rootStore);
  const snapshot = rootSnapshot.page.components[entityId];
  
  // 3. 渲染优化 - 检查是否需要重新渲染
  const shouldRender = useMemo(() => {
    if (!optimization.renderOptimizer) return true;
    
    const renderProps = {
      entityId,
      component: snapshot,
      timestamp: Date.now()
    };
    
    const shouldUpdate = optimization.renderOptimizer.shouldComponentUpdate(
      entityId,
      renderProps
    );
    
    if (shouldUpdate) {
      optimization.monitor?.recordMetric('renderCount', entityId);
    }
    
    return shouldUpdate;
  }, [entityId, snapshot]);

  // 4. 组件不存在时自动创建默认结构
  if (!component && entityId) {
    console.warn(`组件 ${entityId} 不存在，创建默认结构`);
    pageActions.registerComponent({
      entityId,
      compName: 'unknown',
      prop: {},
      state: {},
      modelValue: {}
    } as ComponentNode);
  }

  // 5. 事件处理函数 (缓存优化)
  const executeAction = useCallback(async (
    actions: ActionConfig | ActionConfig[] | any,
    triggerEvent?: string
  ) => {
    try {
      console.log(`组件 ${entityId} 执行事件:`, triggerEvent, actions);
      
      // 转换ComponentAction格式到ActionConfig格式
      const normalizeAction = (action: any): ActionConfig => {
        if (action.type) {
          // 已经是ActionConfig格式
          return action;
        }
        
        // 转换ComponentAction到ActionConfig
        return {
          type: action.action || 'request', // action字段映射到type
          params: action.option || action.params || {}, // option字段映射到params
          condition: action.condition,
          thenActions: action.thenActions?.map(normalizeAction)
        };
      };
      
      const normalizedActions = Array.isArray(actions) 
        ? actions.map(normalizeAction)
        : normalizeAction(actions);
      
      // 使用节流的事件分发器
      const throttledDispatch = optimization.eventThrottleManager.throttle(
        `${entityId}_${triggerEvent || 'default'}`,
        eventDispatcher.dispatch.bind(eventDispatcher),
        100 // 100ms 节流间隔
      );
      
      await throttledDispatch(normalizedActions, {
        currentComponent: component,
        triggerEvent,
        extraData: { entityId }
      });
      
      optimization.monitor?.recordMetric('eventCount', entityId);
    } catch (error) {
      console.error(`组件 ${entityId} 事件执行失败:`, error);
      // 可以在这里添加错误处理逻辑，如显示错误提示
    }
  }, [entityId, component]);

  // 6. 更新组件属性的便捷函数 (批量优化)
  const updateProp = useCallback((propPath: string, value: any, immediate = false) => {
    if (immediate) {
      // 立即更新 (用于重要的UI状态变化)
      pageActions.updateComponentProp(entityId, propPath, value);
    } else {
      // 批量更新 (用于一般状态变化)
      optimization.batchUpdateManager.addUpdate(entityId, { [propPath]: value });
    }
    
    optimization.monitor?.recordMetric('updateCount', entityId);
  }, [entityId]);

  // 7. 批量更新多个属性
  const updateProps = useCallback((updates: Record<string, any>) => {
    optimization.batchUpdateManager.addUpdate(entityId, updates);
    optimization.monitor?.recordMetric('updateCount', entityId);
  }, [entityId]);

  // 8. 性能优化：只在需要时返回新的对象
  return useMemo(() => ({
    component: component || {} as ComponentNode,
    snapshot: snapshot || {} as Readonly<ComponentNode>,
    executeAction,
    updateProp,
    updateProps,
    exists: !!component,
    shouldRender
  }), [component, snapshot, executeAction, updateProp, updateProps, shouldRender]);
}

/**
 * 页面级Hook - 管理整个页面的组件
 */
export interface UsePageComponentsResult {
  // 所有组件快照
  components: Record<string, ComponentNode>;
  // 页面配置快照
  pageConfig: any;
  // 全局数据快照
  globalData: any;
  // 初始化页面组件
  initPage: (components: Record<string, ComponentNode>) => void;
  // 执行页面级事件
  executePageAction: (actions: ActionConfig | ActionConfig[]) => Promise<void>;
}

export function usePageComponents(): UsePageComponentsResult {
  const rootSnapshot = useSnapshot(rootStore);
  
  const initPage = (components: Record<string, ComponentNode>) => {
    pageActions.initComponents(components);
  };

  const executePageAction = async (actions: ActionConfig | ActionConfig[]) => {
    try {
      await eventDispatcher.dispatch(actions, {
        triggerEvent: 'page-action'
      });
    } catch (error) {
      console.error('页面事件执行失败:', error);
    }
  };

  return {
    components: rootSnapshot.page.components as Record<string, Readonly<ComponentNode>>,
    pageConfig: rootSnapshot.page.config,
    globalData: rootSnapshot.global,
    initPage,
    executePageAction
  };
}

/**
 * API数据Hook - 监听API请求状态
 */
export interface UseApiDataResult {
  loading: boolean;
  data: any;
  error: Error | null;
  timestamp?: number;
  exists: boolean;
}

export function useApiData(responseDataKey: string): UseApiDataResult {
  const rootSnapshot = useSnapshot(rootStore);
  const apiData = rootSnapshot.global.apiData[responseDataKey];
  
  return {
    loading: apiData?.loading || false,
    data: apiData?.data || null,
    error: apiData?.error || null,
    timestamp: apiData?.timestamp,
    exists: !!apiData
  };
}

/**
 * 表单Hook - 专门处理表单组件
 */
export interface UseFormResult {
  // 表单值
  modelValue: Record<string, any>;
  // 验证结果
  validationResult?: {
    isValid: boolean;
    errors: string[];
  };
  // 设置表单值
  setValue: (field: string, value: any) => void;
  // 获取表单值
  getValue: (field: string) => any;
  // 重置表单
  resetForm: () => void;
  // 验证表单
  validateForm: (fields?: string[]) => Promise<boolean>;
}

export function useForm(formEntityId: string): UseFormResult {
  const { component, executeAction, updateProp } = useEnhancedComponent(formEntityId);
  
  const setValue = (field: string, value: any) => {
    updateProp(`modelValue.${field}`, value);
  };

  const getValue = (field: string) => {
    return component?.modelValue?.[field];
  };

  const resetForm = () => {
    if (component?.modelValue) {
      Object.keys(component.modelValue).forEach(key => {
        setValue(key, '');
      });
    }
  };

  const validateForm = async (fields?: string[]): Promise<boolean> => {
    try {
      await executeAction({
        type: 'validate',
        params: {
          targetId: formEntityId,
          fields
        }
      });
      return true;
    } catch (error) {
      console.error('表单验证失败:', error);
      return false;
    }
  };

  return {
    modelValue: component?.modelValue || {},
    validationResult: component?.validationResult,
    setValue,
    getValue,
    resetForm,
    validateForm
  };
}

/**
 * 条件显示Hook - 基于表达式控制组件显示
 */
export function useConditionalDisplay(
  condition: string,
  entityId?: string
): boolean {
  const { component } = useEnhancedComponent(entityId || '');
  const rootSnapshot = useSnapshot(rootStore);
  
  // 使用useSyncExternalStore监听条件变化
  const shouldDisplay = useSyncExternalStore(
    // subscribe函数
    (callback) => {
      // 这里应该根据条件表达式中的依赖来订阅相应的状态变化
      // 暂时订阅整个rootStore的变化 (可以优化)
      const { subscribe } = require('valtio');
      return subscribe(rootStore, callback);
    },
    // getSnapshot函数  
    () => {
      if (!condition) return true;
      
      // 使用表达式引擎计算条件
      const { evaluateExpression } = require('../utils/expression-engine');
      return evaluateExpression(condition, {
        currentComponent: component,
        extraContext: { rootStore: rootSnapshot }
      });
    }
  );
  
  return Boolean(shouldDisplay);
}

/**
 * 监听组件事件Hook
 */
export function useComponentEvent(
  entityId: string,
  eventName: string,
  callback: (eventData?: any) => void
) {
  const { component } = useEnhancedComponent(entityId);
  
  // 这里可以实现事件监听机制
  // 暂时简化实现
  console.log(`监听组件 ${entityId} 的事件 ${eventName}`);
}

// ========== 兼容性导出 ==========

/**
 * 兼容旧版本的useReactiveComponent
 * @deprecated 请使用useEnhancedComponent
 */
function useReactiveComponentCompat(entityId: string): ComponentNode {
  console.warn('useReactiveComponent已废弃，请使用useEnhancedComponent');
  const { component } = useEnhancedComponent(entityId);
  return component;
}

export default useEnhancedComponent;
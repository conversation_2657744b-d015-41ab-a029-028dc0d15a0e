/**
 * 代码分割生成器
 * 为AST代码生成器添加懒加载和代码分割支持
 */

import * as t from '@babel/types';
import { ComponentNode, ParsedComponentTree } from '../types/component.types';
import { kebabToPascalCase } from '../utils/naming-utils';
import { createRequire } from 'module';

const requireModule = createRequire(import.meta.url);

// ========== 类型定义 ==========

export interface CodeSplittingOptions {
  /** 启用路由级代码分割 */
  enableRouteSplitting: boolean;
  /** 启用组件级代码分割 */
  enableComponentSplitting: boolean;
  /** 组件大小阈值(KB)，超过此阈值的组件将被分割 */
  componentSizeThreshold: number;
  /** 预加载策略 */
  preloadStrategy: 'none' | 'hover' | 'viewport' | 'idle';
  /** 是否生成加载状态组件 */
  generateLoadingStates: boolean;
  /** 是否生成错误边界 */
  generateErrorBoundary: boolean;
}

export interface SplitChunk {
  /** 分块名称 */
  name: string;
  /** 分块类型 */
  type: 'route' | 'component' | 'vendor';
  /** 包含的组件 */
  components: string[];
  /** 预估大小(KB) */
  estimatedSize: number;
  /** 是否需要预加载 */
  preload: boolean;
}

export interface CodeSplittingResult {
  /** 主入口代码 */
  entryCode: string;
  /** 分割的代码块 */
  chunks: Map<string, string>;
  /** 分块信息 */
  chunkInfo: SplitChunk[];
  /** 预加载清单 */
  preloadManifest: string[];
}

// ========== 代码分割生成器 ==========

export class CodeSplittingGenerator {
  private options: CodeSplittingOptions;
  private componentNodes: Map<string, ComponentNode> = new Map();
  private splitChunks: SplitChunk[] = [];

  constructor(options: Partial<CodeSplittingOptions> = {}) {
    this.options = {
      enableRouteSplitting: true,
      enableComponentSplitting: true,
      componentSizeThreshold: 50, // 50KB
      preloadStrategy: 'hover',
      generateLoadingStates: true,
      generateErrorBoundary: true,
      ...options
    };
  }

  /**
   * 分析组件并生成代码分割方案
   */
  public async analyzeAndSplit(
    trees: ParsedComponentTree[],
    componentNodes: Map<string, ComponentNode>
  ): Promise<CodeSplittingResult> {
    this.componentNodes = componentNodes;
    
    console.log('🔄 开始代码分割分析...');

    // 1. 分析组件大小和依赖关系
    const componentAnalysis = this.analyzeComponents();
    
    // 2. 生成分割策略
    this.generateSplitStrategy(componentAnalysis);
    
    // 3. 生成代码
    const result = await this.generateSplitCode(trees);
    
    console.log(`✅ 代码分割完成: ${this.splitChunks.length} 个分块`);
    return result;
  }

  /**
   * 分析组件大小和复杂度
   */
  private analyzeComponents(): Map<string, ComponentAnalysis> {
    const analysis = new Map<string, ComponentAnalysis>();
    
    this.componentNodes.forEach((component, entityId) => {
      const complexity = this.calculateComponentComplexity(component);
      const estimatedSize = this.estimateComponentSize(component);
      const dependencies = this.findComponentDependencies(component);
      
      analysis.set(entityId, {
        entityId,
        component,
        complexity,
        estimatedSize,
        dependencies,
        shouldSplit: estimatedSize > this.options.componentSizeThreshold
      });
    });
    
    return analysis;
  }

  /**
   * 计算组件复杂度
   */
  private calculateComponentComplexity(component: ComponentNode): number {
    let complexity = 1; // 基础复杂度
    
    // 根据属性数量增加复杂度
    if (component.prop) {
      complexity += Object.keys(component.prop).length * 0.5;
    }
    
    // 根据事件数量增加复杂度
    if (component.actions && component.actions.length > 0) {
      complexity += component.actions.length * 2;
    }
    
    // 根据子组件数量增加复杂度
    if (component.childrens && component.childrens.length > 0) {
      complexity += component.childrens.length * 1.5;
    }
    
    return Math.round(complexity * 10) / 10;
  }

  /**
   * 估算组件大小(KB)
   */
  private estimateComponentSize(component: ComponentNode): number {
    let size = 2; // 基础大小(KB)
    
    // 根据组件类型调整大小
    switch (component.compName) {
      case 'lyy-table':
      case 'lyy-form':
        size += 15; // 复杂组件
        break;
      case 'lyy-chart':
      case 'lyy-editor':
        size += 25; // 大型组件
        break;
      default:
        size += 3; // 普通组件
    }
    
    // 根据属性和事件增加大小
    if (component.prop) {
      size += Object.keys(component.prop).length * 0.3;
    }
    if (component.actions) {
      size += component.actions.length * 0.5;
    }
    
    return Math.round(size * 10) / 10;
  }

  /**
   * 查找组件依赖
   */
  private findComponentDependencies(component: ComponentNode): string[] {
    const dependencies: string[] = [];
    
    // 分析事件中的依赖
    if (component.actions) {
      component.actions.forEach(action => {
        if (action.option && typeof action.option === 'object') {
          // 查找引用其他组件的action
          if ('targetComponentId' in action.option && action.option.targetComponentId) {
            dependencies.push(action.option.targetComponentId);
          }
        }
      });
    }
    
    return [...new Set(dependencies)];
  }

  /**
   * 生成分割策略
   */
  private generateSplitStrategy(analysis: Map<string, ComponentAnalysis>): void {
    this.splitChunks = [];
    
    if (this.options.enableRouteSplitting) {
      this.generateRouteSplits(analysis);
    }
    
    if (this.options.enableComponentSplitting) {
      this.generateComponentSplits(analysis);
    }
    
    this.generateVendorSplit();
  }

  /**
   * 生成路由级分割
   */
  private generateRouteSplits(analysis: Map<string, ComponentAnalysis>): void {
    // 将组件按页面分组
    const pageGroups = new Map<string, string[]>();
    
    analysis.forEach((info, entityId) => {
      const pageName = this.extractPageName(entityId);
      if (!pageGroups.has(pageName)) {
        pageGroups.set(pageName, []);
      }
      pageGroups.get(pageName)!.push(entityId);
    });
    
    // 为每个页面创建分块
    pageGroups.forEach((components, pageName) => {
      const totalSize = components.reduce((sum, id) => {
        return sum + (analysis.get(id)?.estimatedSize || 0);
      }, 0);
      
      this.splitChunks.push({
        name: `page-${pageName}`,
        type: 'route',
        components,
        estimatedSize: totalSize,
        preload: pageName === 'home' // 首页预加载
      });
    });
  }

  /**
   * 生成组件级分割
   */
  private generateComponentSplits(analysis: Map<string, ComponentAnalysis>): void {
    analysis.forEach((info, entityId) => {
      if (info.shouldSplit) {
        this.splitChunks.push({
          name: `component-${kebabToPascalCase(info.component.compName)}-${entityId}`,
          type: 'component',
          components: [entityId],
          estimatedSize: info.estimatedSize,
          preload: this.shouldPreloadComponent(info)
        });
      }
    });
  }

  /**
   * 生成第三方库分割
   */
  private generateVendorSplit(): void {
    this.splitChunks.push({
      name: 'vendor',
      type: 'vendor',
      components: [], // 第三方库不包含组件
      estimatedSize: 100, // 预估大小
      preload: true // 第三方库预加载
    });
  }

  /**
   * 生成分割后的代码
   */
  private async generateSplitCode(trees: ParsedComponentTree[]): Promise<CodeSplittingResult> {
    const entryCode = await this.generateEntryCode();
    const chunks = new Map<string, string>();
    
    // 为每个分块生成代码
    for (const chunk of this.splitChunks) {
      if (chunk.type !== 'vendor') {
        chunks.set(chunk.name, await this.generateChunkCode(chunk));
      }
    }
    
    return {
      entryCode,
      chunks,
      chunkInfo: this.splitChunks,
      preloadManifest: this.generatePreloadManifest()
    };
  }

  /**
   * 生成主入口代码
   */
  private async generateEntryCode(): Promise<string> {
    const statements: t.Statement[] = [];
    
    // 1. 基础导入
    statements.push(...this.generateBaseImports());
    
    // 2. 懒加载组件导入
    statements.push(...this.generateLazyImports());
    
    // 3. 加载状态组件
    if (this.options.generateLoadingStates) {
      statements.push(this.generateLoadingComponent());
    }
    
    // 4. 错误边界组件
    if (this.options.generateErrorBoundary) {
      statements.push(this.generateErrorBoundary());
    }
    
    // 5. 主应用组件
    statements.push(this.generateMainApp());
    
    // 6. 预加载逻辑
    statements.push(this.generatePreloadLogic());
    
    // 7. 导出
    statements.push(this.generateExport());
    
    const program = t.program(statements, [], 'module');
    try {
      const babelGenerator = requireModule('@babel/generator');
      const generate = babelGenerator.default || babelGenerator;
      return generate(program, { compact: false }).code;
    } catch (e) {
      // Fallback for dynamic import
      const babelGenerator = await import('@babel/generator');
      const generate = (babelGenerator as any).default || babelGenerator;
      return generate(program, { compact: false }).code;
    }
  }

  /**
   * 生成基础导入语句
   */
  private generateBaseImports(): t.Statement[] {
    return [
      // React导入
      t.importDeclaration(
        [
          t.importDefaultSpecifier(t.identifier('React')),
          t.importSpecifier(t.identifier('Suspense'), t.identifier('Suspense')),
          t.importSpecifier(t.identifier('lazy'), t.identifier('lazy')),
          t.importSpecifier(t.identifier('useEffect'), t.identifier('useEffect'))
        ],
        t.stringLiteral('react')
      ),
      
      // 核心库导入
      t.importDeclaration(
        [
          t.importSpecifier(t.identifier('usePageComponents'), t.identifier('usePageComponents')),
          t.importSpecifier(t.identifier('pluginManager'), t.identifier('pluginManager'))
        ],
        t.stringLiteral('@lyy/core')
      )
    ];
  }

  /**
   * 生成懒加载导入语句
   */
  private generateLazyImports(): t.Statement[] {
    const imports: t.Statement[] = [];
    
    this.splitChunks
      .filter(chunk => chunk.type === 'route' || chunk.type === 'component')
      .forEach(chunk => {
        const componentName = this.getChunkComponentName(chunk);
        
        // const LazyComponent = React.lazy(() => import('./chunks/chunk-name'))
        const lazyDeclaration = t.variableDeclaration('const', [
          t.variableDeclarator(
            t.identifier(componentName),
            t.callExpression(
              t.memberExpression(t.identifier('React'), t.identifier('lazy')),
              [
                t.arrowFunctionExpression(
                  [],
                  t.callExpression(
                    t.identifier('import'),
                    [t.stringLiteral(`./chunks/${chunk.name}`)]
                  )
                )
              ]
            )
          )
        ]);
        
        imports.push(lazyDeclaration);
      });
    
    return imports;
  }

  /**
   * 生成加载状态组件
   */
  private generateLoadingComponent(): t.Statement {
    // const LoadingSpinner = () => <div className="loading">加载中...</div>
    const loadingComponent = t.variableDeclaration('const', [
      t.variableDeclarator(
        t.identifier('LoadingSpinner'),
        t.arrowFunctionExpression(
          [],
          t.jsxElement(
            t.jsxOpeningElement(t.jsxIdentifier('div'), [
              t.jsxAttribute(t.jsxIdentifier('className'), t.stringLiteral('loading'))
            ]),
            t.jsxClosingElement(t.jsxIdentifier('div')),
            [t.jsxText('加载中...')]
          )
        )
      )
    ]);
    
    return loadingComponent;
  }

  /**
   * 生成错误边界组件
   */
  private generateErrorBoundary(): t.Statement {
    // class ErrorBoundary extends React.Component { ... }
    const errorBoundaryClass = t.classDeclaration(
      t.identifier('ErrorBoundary'),
      t.memberExpression(t.identifier('React'), t.identifier('Component')),
      t.classBody([
        // constructor
        t.classMethod('constructor', t.identifier('constructor'), [t.identifier('props')], 
          t.blockStatement([
            t.expressionStatement(t.callExpression(t.super(), [t.identifier('props')])),
            t.expressionStatement(t.assignmentExpression('=', 
              t.memberExpression(t.thisExpression(), t.identifier('state')),
              t.objectExpression([
                t.objectProperty(t.identifier('hasError'), t.booleanLiteral(false))
              ])
            ))
          ])
        ),
        
        // static getDerivedStateFromError
        t.classMethod('method', t.identifier('getDerivedStateFromError'), [t.identifier('error')],
          t.blockStatement([
            t.returnStatement(t.objectExpression([
              t.objectProperty(t.identifier('hasError'), t.booleanLiteral(true))
            ]))
          ]),
          false, true
        ),
        
        // render
        t.classMethod('method', t.identifier('render'), [],
          t.blockStatement([
            t.ifStatement(
              t.memberExpression(
                t.memberExpression(t.thisExpression(), t.identifier('state')),
                t.identifier('hasError')
              ),
              t.returnStatement(
                t.jsxElement(
                  t.jsxOpeningElement(t.jsxIdentifier('div'), [
                    t.jsxAttribute(t.jsxIdentifier('className'), t.stringLiteral('error-boundary'))
                  ]),
                  t.jsxClosingElement(t.jsxIdentifier('div')),
                  [t.jsxText('组件加载失败，请刷新页面重试')]
                )
              )
            ),
            t.returnStatement(
              t.memberExpression(
                t.memberExpression(t.thisExpression(), t.identifier('props')),
                t.identifier('children')
              )
            )
          ])
        )
      ])
    );
    
    return errorBoundaryClass;
  }

  /**
   * 生成主应用组件
   */
  private generateMainApp(): t.Statement {
    // function App() { ... }
    const appFunction = t.functionDeclaration(
      t.identifier('App'),
      [],
      t.blockStatement([
        // 初始化插件管理器
        t.expressionStatement(
          t.callExpression(
            t.identifier('useEffect'),
            [
              t.arrowFunctionExpression([], 
                t.callExpression(
                  t.memberExpression(t.identifier('pluginManager'), t.identifier('initialize')),
                  []
                )
              ),
              t.arrayExpression([])
            ]
          )
        ),
        
        // 返回JSX
        t.returnStatement(
          t.jsxElement(
            t.jsxOpeningElement(t.jsxIdentifier('ErrorBoundary'), []),
            t.jsxClosingElement(t.jsxIdentifier('ErrorBoundary')),
            [
              t.jsxElement(
                t.jsxOpeningElement(t.jsxIdentifier('Suspense'), [
                  t.jsxAttribute(
                    t.jsxIdentifier('fallback'),
                    t.jsxExpressionContainer(
                      t.jsxElement(
                        t.jsxOpeningElement(t.jsxIdentifier('LoadingSpinner'), [], true),
                        null,
                        []
                      )
                    )
                  )
                ]),
                t.jsxClosingElement(t.jsxIdentifier('Suspense')),
                this.generateAppContent()
              )
            ]
          )
        )
      ])
    );
    
    return appFunction;
  }

  /**
   * 生成应用内容
   */
  private generateAppContent(): t.JSXElement[] {
    // 根据分块生成路由结构
    const routeComponents = this.splitChunks
      .filter(chunk => chunk.type === 'route')
      .map(chunk => {
        const componentName = this.getChunkComponentName(chunk);
        return t.jsxElement(
          t.jsxOpeningElement(t.jsxIdentifier(componentName), [], true),
          null,
          []
        );
      });
    
    return routeComponents.length > 0 ? routeComponents : [
      t.jsxElement(
        t.jsxOpeningElement(t.jsxIdentifier('div'), []),
        t.jsxClosingElement(t.jsxIdentifier('div')),
        [t.jsxText('应用内容')]
      )
    ];
  }

  /**
   * 生成预加载逻辑
   */
  private generatePreloadLogic(): t.Statement {
    const preloadChunks = this.splitChunks.filter(chunk => chunk.preload);
    
    if (preloadChunks.length === 0) {
      return t.expressionStatement(t.identifier('undefined'));
    }
    
    // useEffect(() => { 预加载逻辑 }, [])
    const preloadEffect = t.expressionStatement(
      t.callExpression(
        t.identifier('useEffect'),
        [
          t.arrowFunctionExpression([], 
            t.blockStatement(
              preloadChunks.map(chunk => 
                t.expressionStatement(
                  t.callExpression(
                    t.identifier('import'),
                    [t.stringLiteral(`./chunks/${chunk.name}`)]
                  )
                )
              )
            )
          ),
          t.arrayExpression([])
        ]
      )
    );
    
    return preloadEffect;
  }

  /**
   * 生成导出语句
   */
  private generateExport(): t.Statement {
    return t.exportDefaultDeclaration(t.identifier('App'));
  }

  /**
   * 生成分块代码
   */
  private async generateChunkCode(chunk: SplitChunk): Promise<string> {
    const statements: t.Statement[] = [];
    
    // 导入相关组件
    statements.push(...this.generateChunkImports(chunk));
    
    // 生成分块组件
    statements.push(this.generateChunkComponent(chunk));
    
    // 导出
    statements.push(this.generateChunkExport(chunk));
    
    const program = t.program(statements, [], 'module');
    try {
      const babelGenerator = requireModule('@babel/generator');
      const generate = babelGenerator.default || babelGenerator;
      return generate(program, { compact: false }).code;
    } catch (e) {
      // Fallback for dynamic import
      const babelGenerator = await import('@babel/generator');
      const generate = (babelGenerator as any).default || babelGenerator;
      return generate(program, { compact: false }).code;
    }
  }

  /**
   * 生成分块导入
   */
  private generateChunkImports(chunk: SplitChunk): t.Statement[] {
    const imports: t.Statement[] = [];
    
    // React导入
    imports.push(
      t.importDeclaration(
        [t.importDefaultSpecifier(t.identifier('React'))],
        t.stringLiteral('react')
      )
    );
    
    // 组件库导入
    const componentNames = chunk.components
      .map(id => this.componentNodes.get(id))
      .filter(Boolean)
      .map(comp => kebabToPascalCase(comp!.compName));
    
    if (componentNames.length > 0) {
      imports.push(
        t.importDeclaration(
          componentNames.map(name => 
            t.importSpecifier(t.identifier(name), t.identifier(name))
          ),
          t.stringLiteral('@lyy/components')
        )
      );
    }
    
    return imports;
  }

  /**
   * 生成分块组件
   */
  private generateChunkComponent(chunk: SplitChunk): t.Statement {
    const componentName = this.getChunkComponentName(chunk);
    
    return t.functionDeclaration(
      t.identifier(componentName),
      [],
      t.blockStatement([
        t.returnStatement(
          t.jsxElement(
            t.jsxOpeningElement(t.jsxIdentifier('div'), [
              t.jsxAttribute(t.jsxIdentifier('className'), t.stringLiteral(`chunk-${chunk.name}`))
            ]),
            t.jsxClosingElement(t.jsxIdentifier('div')),
            [t.jsxText(`${chunk.name} 分块内容`)]
          )
        )
      ])
    );
  }

  /**
   * 生成分块导出
   */
  private generateChunkExport(chunk: SplitChunk): t.Statement {
    const componentName = this.getChunkComponentName(chunk);
    return t.exportDefaultDeclaration(t.identifier(componentName));
  }

  /**
   * 生成预加载清单
   */
  private generatePreloadManifest(): string[] {
    return this.splitChunks
      .filter(chunk => chunk.preload)
      .map(chunk => `./chunks/${chunk.name}.js`);
  }

  // ========== 辅助方法 ==========

  private extractPageName(entityId: string): string {
    // 从entityId提取页面名，如 "page-home-button-1" => "home"
    const parts = entityId.split('-');
    return parts.length > 1 ? parts[1] : 'default';
  }

  private shouldPreloadComponent(info: ComponentAnalysis): boolean {
    // 大型组件且复杂度高的需要预加载
    return info.complexity > 5 && info.estimatedSize > 20;
  }

  private getChunkComponentName(chunk: SplitChunk): string {
    return `Lazy${kebabToPascalCase(chunk.name)}`;
  }
}

// ========== 辅助类型 ==========

interface ComponentAnalysis {
  entityId: string;
  component: ComponentNode;
  complexity: number;
  estimatedSize: number;
  dependencies: string[];
  shouldSplit: boolean;
}

export default CodeSplittingGenerator;
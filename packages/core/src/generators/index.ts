/**
 * 代码生成器模块导出 (仅新架构)
 */

export { ASTCodeGenerator } from './ast-code-generator';
export { ExpressionASTProcessor } from './expression-ast-processor';
export { CodeSplittingGenerator } from './code-splitting-generator';

// 类型导出
export type { 
  CodeGenerationOptions,
  GeneratedCodeResult 
} from './ast-code-generator';

export type {
  ExpressionInfo,
  ExpressionContext
} from './expression-ast-processor';

export type {
  CodeSplittingOptions,
  SplitChunk,
  CodeSplittingResult
} from './code-splitting-generator';
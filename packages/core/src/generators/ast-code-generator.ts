/**
 * 基于AST的代码生成器
 * 集成新架构的三层状态系统、17种事件处理、表达式引擎
 * 
 * 技术特性:
 * 1. Babel AST 精确代码生成
 * 2. 类型安全的 TypeScript 代码
 * 3. 完整的事件系统支持
 * 4. 表达式引擎集成
 * 5. 性能优化代码生成
 */

import * as t from '@babel/types';
import generator from '@babel/generator';
import { parse } from '@babel/parser';
import traverse from '@babel/traverse';

// 处理ES模块兼容性
const generate = (generator as any).default || generator;
import { ComponentNode, ParsedComponentTree } from '../types/component.types';
import { kebabToPascalCase } from '../utils/naming-utils';
import { CodeSplittingGenerator } from './code-splitting-generator';

// ========== 类型定义 ==========

export interface CodeGenerationOptions {
  enablePerformanceOptimization: boolean;
  generateTypeDefinitions: boolean;
  enableExpressionEngine: boolean;
  enableEventSystem: boolean;
  targetFramework: 'taro' | 'react';
  // 新增代码分割选项
  enableCodeSplitting: boolean;
  codeSplittingThreshold: number; // KB
  enableLazyLoading: boolean;
}

export interface GeneratedCodeResult {
  code: string;
  sourceMap?: string;
  imports: string[];
  exports: string[];
  components: string[];
  // 新增代码分割结果
  splitChunks?: Map<string, string>;
  chunkManifest?: string[];
}

// ========== AST代码生成器 ==========

export class ASTCodeGenerator {
  private options: CodeGenerationOptions;
  private imports: Set<string> = new Set();
  private componentNodes: Map<string, ComponentNode> = new Map();
  private codeSplittingGenerator?: CodeSplittingGenerator;

  constructor(options: Partial<CodeGenerationOptions> = {}) {
    this.options = {
      enablePerformanceOptimization: true,
      generateTypeDefinitions: true,
      enableExpressionEngine: true,
      enableEventSystem: true,
      targetFramework: 'taro',
      // 代码分割默认配置 - 暂时禁用直到chunk文件生成完善
      enableCodeSplitting: false,
      codeSplittingThreshold: 50, // 50KB
      enableLazyLoading: false,
      ...options
    };

    // 初始化代码分割生成器
    if (this.options.enableCodeSplitting) {
      this.codeSplittingGenerator = new CodeSplittingGenerator({
        enableRouteSplitting: true,
        enableComponentSplitting: true,
        componentSizeThreshold: this.options.codeSplittingThreshold,
        preloadStrategy: 'hover',
        generateLoadingStates: true,
        generateErrorBoundary: true
      });
    }
  }

  /**
   * 生成完整的页面代码
   */
  public async generatePageCode(trees: ParsedComponentTree[]): Promise<GeneratedCodeResult> {
    console.log('🚀 开始基于AST的代码生成...');

    // 1. 提取和分析组件数据
    this.analyzeComponentTrees(trees);

    // 2. 代码分割处理
    if (this.options.enableCodeSplitting && this.codeSplittingGenerator) {
      console.log('📦 启用代码分割模式...');
      const splitResult = await this.codeSplittingGenerator.analyzeAndSplit(trees, this.componentNodes);
      
      return {
        code: splitResult.entryCode,
        imports: Array.from(this.imports),
        exports: ['App'],
        components: Array.from(this.componentNodes.keys()),
        splitChunks: splitResult.chunks,
        chunkManifest: splitResult.preloadManifest
      };
    }

    // 3. 传统单文件生成模式
    const program = this.buildProgram(trees);

    // 4. 生成最终代码
    const result = generate(program, {
      retainLines: false,
      compact: false,
      comments: true
    });

    console.log('✅ AST代码生成完成');

    return {
      code: result.code,
      sourceMap: result.map,
      imports: Array.from(this.imports),
      exports: ['GeneratedPage'],
      components: Array.from(this.componentNodes.keys())
    };
  }

  /**
   * 分析组件树结构
   */
  private analyzeComponentTrees(trees: ParsedComponentTree[]): void {
    const traverse = (node: ParsedComponentTree) => {
      this.componentNodes.set(node.entityId, {
        entityId: node.entityId,
        compName: node.compName,
        prop: node.prop || {},
        state: {},
        modelValue: (node as any).modelValue || {},
        actions: node.actions || []
      } as ComponentNode);

      if (node.children) {
        node.children.forEach(child => traverse(child));
      }
    };

    trees.forEach(tree => traverse(tree));
    console.log(`📊 分析完成: ${this.componentNodes.size} 个组件`);
  }

  /**
   * 构建AST程序结构
   */
  private buildProgram(trees: ParsedComponentTree[]): t.Program {
    const body: t.Statement[] = [];

    // 1. 添加导入语句
    body.push(...this.generateImportStatements());

    // 2. 添加组件数据常量
    body.push(this.generateComponentDataDeclaration());

    // 3. 添加主页面组件
    body.push(this.generateMainPageComponent(trees));

    // 4. 添加导出语句
    body.push(this.generateExportStatement());

    return t.program(body, [], 'module');
  }

  /**
   * 生成导入语句
   */
  private generateImportStatements(): t.Statement[] {
    const imports: t.Statement[] = [];

    // React 导入
    imports.push(
      t.importDeclaration(
        [
          t.importDefaultSpecifier(t.identifier('React')),
          t.importSpecifier(t.identifier('useEffect'), t.identifier('useEffect'))
        ],
        t.stringLiteral('react')
      )
    );

    // 新架构 Hooks 导入
    imports.push(
      t.importDeclaration(
        [
          t.importSpecifier(t.identifier('useEnhancedComponent'), t.identifier('useEnhancedComponent')),
          t.importSpecifier(t.identifier('usePageComponents'), t.identifier('usePageComponents')),
          t.importSpecifier(t.identifier('useForm'), t.identifier('useForm')),
          t.importSpecifier(t.identifier('evaluateExpression'), t.identifier('evaluateExpression'))
        ],
        t.stringLiteral('@lyy/core')
      )
    );

    // 组件库导入 - 使用命名导入
    const componentNames = Array.from(
      new Set(Array.from(this.componentNodes.values()).map(c => kebabToPascalCase(c.compName)))
    );
    
    if (componentNames.length > 0) {
      imports.push(
        t.importDeclaration(
          componentNames.map(componentName => 
            t.importSpecifier(t.identifier(componentName), t.identifier(componentName))
          ),
          t.stringLiteral('@lyy/components')
        )
      );
    }

    // Taro 导入 (如果目标框架是 Taro)
    if (this.options.targetFramework === 'taro') {
      imports.push(
        t.importDeclaration(
          [t.importDefaultSpecifier(t.identifier('Taro'))],
          t.stringLiteral('@tarojs/taro')
        )
      );
    }

    return imports;
  }

  /**
   * 生成组件数据声明
   */
  private generateComponentDataDeclaration(): t.Statement {
    const componentData: Record<string, any> = {};
    
    this.componentNodes.forEach((component, entityId) => {
      componentData[entityId] = {
        entityId: component.entityId,
        compName: component.compName,
        prop: component.prop,
        state: (component as any).state || {},
        modelValue: component.modelValue || {}
      };
    });

    return t.variableDeclaration('const', [
      t.variableDeclarator(
        t.identifier('COMPONENT_DATA'),
        this.objectToAST(componentData)
      )
    ]);
  }

  /**
   * 生成主页面组件
   */
  private generateMainPageComponent(trees: ParsedComponentTree[]): t.Statement {
    const body: t.Statement[] = [];

    // 1. 页面级Hooks调用
    body.push(...this.generatePageHooks());

    // 2. 组件初始化Effect
    body.push(this.generateComponentInitEffect());

    // 3. 各组件的Hooks调用
    body.push(...this.generateComponentHooks());

    // 4. 事件处理函数
    body.push(...this.generateEventHandlers());

    // 5. 表达式计算函数
    if (this.options.enableExpressionEngine) {
      body.push(...this.generateExpressionHandlers());
    }

    // 6. 返回JSX
    body.push(
      t.returnStatement(
        this.generateJSXElement(trees)
      )
    );

    // 创建函数组件
    const pageComponent = t.functionDeclaration(
      t.identifier('GeneratedPage'),
      [],
      t.blockStatement(body)
    );

    return pageComponent;
  }

  /**
   * 生成页面级Hooks
   */
  private generatePageHooks(): t.Statement[] {
    const hooks: t.Statement[] = [];

    // usePageComponents Hook
    hooks.push(
      t.variableDeclaration('const', [
        t.variableDeclarator(
          t.objectPattern([
            t.objectProperty(t.identifier('initPage'), t.identifier('initPage')),
            t.objectProperty(t.identifier('components'), t.identifier('pageComponents')),
            t.objectProperty(t.identifier('executePageAction'), t.identifier('executePageAction'))
          ]),
          t.callExpression(t.identifier('usePageComponents'), [])
        )
      ])
    );

    return hooks;
  }

  /**
   * 生成组件初始化Effect
   */
  private generateComponentInitEffect(): t.Statement {
    return t.expressionStatement(
      t.callExpression(t.identifier('useEffect'), [
        t.arrowFunctionExpression(
          [],
          t.blockStatement([
            t.expressionStatement(
              t.callExpression(t.identifier('initPage'), [
                t.identifier('COMPONENT_DATA')
              ])
            )
          ])
        ),
        t.arrayExpression([])
      ])
    );
  }

  /**
   * 生成组件级Hooks调用
   */
  private generateComponentHooks(): t.Statement[] {
    const hooks: t.Statement[] = [];

    this.componentNodes.forEach((component, entityId) => {
      // 为每个组件生成 useEnhancedComponent Hook
      hooks.push(
        t.variableDeclaration('const', [
          t.variableDeclarator(
            t.objectPattern([
              t.objectProperty(t.identifier('component'), t.identifier(`${entityId}_component`)),
              t.objectProperty(t.identifier('snapshot'), t.identifier(`${entityId}_snapshot`)),
              t.objectProperty(t.identifier('executeAction'), t.identifier(`${entityId}_executeAction`)),
              t.objectProperty(t.identifier('updateProp'), t.identifier(`${entityId}_updateProp`))
            ]),
            t.callExpression(t.identifier('useEnhancedComponent'), [
              t.stringLiteral(entityId)
            ])
          )
        ])
      );

      // 如果是表单组件，添加 useForm Hook
      if (component.compName === 'lyy-form') {
        hooks.push(
          t.variableDeclaration('const', [
            t.variableDeclarator(
              t.objectPattern([
                t.objectProperty(t.identifier('modelValue'), t.identifier(`${entityId}_modelValue`)),
                t.objectProperty(t.identifier('setValue'), t.identifier(`${entityId}_setValue`)),
                t.objectProperty(t.identifier('validateForm'), t.identifier(`${entityId}_validateForm`))
              ]),
              t.callExpression(t.identifier('useForm'), [
                t.stringLiteral(entityId)
              ])
            )
          ])
        );
      }
    });

    return hooks;
  }

  /**
   * 生成事件处理函数
   */
  private generateEventHandlers(): t.Statement[] {
    const handlers: t.Statement[] = [];

    if (!this.options.enableEventSystem) {
      return handlers;
    }

    this.componentNodes.forEach((component, entityId) => {
      if (component.actions && component.actions.length > 0) {
        component.actions.forEach((action, index) => {
          const handlerName = `handle_${entityId}_${action.event}_${index}`;
          
          // 生成事件处理函数
          const handlerFunction = t.functionDeclaration(
            t.identifier(handlerName),
            [],
            t.blockStatement([
              t.expressionStatement(
                t.callExpression(
                  t.identifier(`${entityId}_executeAction`),
                  [
                    this.objectToAST(action),
                    t.stringLiteral(action.event)
                  ]
                )
              )
            ])
          );

          // 如果是异步事件，添加async标识
          if (this.isAsyncAction(action.action)) {
            handlerFunction.async = true;
          }

          handlers.push(handlerFunction);
        });
      }
    });

    return handlers;
  }

  /**
   * 生成表达式处理函数
   */
  private generateExpressionHandlers(): t.Statement[] {
    const handlers: t.Statement[] = [];

    // 查找所有包含表达式的属性
    this.componentNodes.forEach((component, entityId) => {
      this.findExpressions(component).forEach((expr, index) => {
        const handlerName = `evaluate_${entityId}_expr_${index}`;
        
        handlers.push(
          t.functionDeclaration(
            t.identifier(handlerName),
            [],
            t.blockStatement([
              t.returnStatement(
                t.callExpression(
                  t.identifier('evaluateExpression'),
                  [
                    t.stringLiteral(expr),
                    t.objectExpression([
                      t.objectProperty(
                        t.identifier('currentComponent'),
                        t.identifier(`${entityId}_component`)
                      )
                    ])
                  ]
                )
              )
            ])
          )
        );
      });
    });

    return handlers;
  }

  /**
   * 生成JSX元素
   */
  private generateJSXElement(trees: ParsedComponentTree[]): t.JSXElement | t.JSXFragment {
    const elements = trees.map(tree => this.componentTreeToJSX(tree));
    
    if (elements.length === 1) {
      return elements[0];
    }

    return t.jsxFragment(
      t.jsxOpeningFragment(),
      t.jsxClosingFragment(),
      elements
    );
  }

  /**
   * 组件树转JSX
   */
  private componentTreeToJSX(tree: ParsedComponentTree): t.JSXElement {
    const componentName = kebabToPascalCase(tree.compName);
    
    // 构建属性
    const attributes: t.JSXAttribute[] = [
      t.jsxAttribute(
        t.jsxIdentifier('entityId'),
        t.stringLiteral(tree.entityId)
      ),
      t.jsxAttribute(
        t.jsxIdentifier('compName'),
        t.stringLiteral(tree.compName)
      ),
      t.jsxAttribute(
        t.jsxIdentifier('prop'),
        t.jsxExpressionContainer(
          t.memberExpression(
            t.identifier(`${tree.entityId}_snapshot`),
            t.identifier('prop')
          )
        )
      )
    ];

    // 添加事件处理属性
    if (tree.actions && tree.actions.length > 0) {
      tree.actions.forEach((action, index) => {
        const eventName = this.mapEventToJSXProp(action.event);
        if (eventName) {
          const handlerName = `handle_${tree.entityId}_${action.event}_${index}`;
          attributes.push(
            t.jsxAttribute(
              t.jsxIdentifier(eventName),
              t.jsxExpressionContainer(t.identifier(handlerName))
            )
          );
        }
      });
    }

    // 处理子组件
    const children: t.JSXElement[] = [];
    if (tree.children && tree.children.length > 0) {
      children.push(...tree.children.map(child => this.componentTreeToJSX(child)));
    }

    return t.jsxElement(
      t.jsxOpeningElement(t.jsxIdentifier(componentName), attributes),
      t.jsxClosingElement(t.jsxIdentifier(componentName)),
      children,
      false
    );
  }

  /**
   * 生成导出语句
   */
  private generateExportStatement(): t.Statement {
    return t.exportDefaultDeclaration(t.identifier('GeneratedPage'));
  }

  // ========== 工具函数 ==========

  /**
   * 对象转AST节点
   */
  private objectToAST(obj: any): t.Expression {
    if (obj === null) {
      return t.nullLiteral();
    }
    
    if (typeof obj === 'string') {
      return t.stringLiteral(obj);
    }
    
    if (typeof obj === 'number') {
      return t.numericLiteral(obj);
    }
    
    if (typeof obj === 'boolean') {
      return t.booleanLiteral(obj);
    }
    
    if (Array.isArray(obj)) {
      return t.arrayExpression(obj.map(item => this.objectToAST(item)));
    }
    
    if (typeof obj === 'object') {
      const properties: t.ObjectProperty[] = [];
      Object.keys(obj).forEach(key => {
        properties.push(
          t.objectProperty(
            t.stringLiteral(key),
            this.objectToAST(obj[key])
          )
        );
      });
      return t.objectExpression(properties);
    }
    
    return t.identifier('undefined');
  }

  /**
   * 检查动作是否为异步
   */
  private isAsyncAction(action: string): boolean {
    const asyncActions = ['request', 'upload', 'scanCode', 'requestDataset', 'validate'];
    return asyncActions.includes(action);
  }

  /**
   * 查找组件中的表达式
   */
  private findExpressions(component: ComponentNode): string[] {
    const expressions: string[] = [];
    
    const searchInObject = (obj: any) => {
      if (typeof obj === 'string' && obj.includes('{{') && obj.includes('}}')) {
        expressions.push(obj);
      } else if (typeof obj === 'object' && obj !== null) {
        Object.values(obj).forEach(value => searchInObject(value));
      }
    };

    searchInObject(component.prop);
    return expressions;
  }

  /**
   * 事件名映射到JSX属性
   */
  private mapEventToJSXProp(eventName: string): string | null {
    const eventMap: Record<string, string> = {
      'click': 'onClick',
      'beforeMount': 'onBeforeMount',
      'afterMount': 'onAfterMount',
      'submit': 'onSubmit',
      'change': 'onChange',
      'input': 'onInput',
      'focus': 'onFocus',
      'blur': 'onBlur'
    };
    
    return eventMap[eventName] || null;
  }
}

export default ASTCodeGenerator;
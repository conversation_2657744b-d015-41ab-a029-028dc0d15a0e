import { ParsedComponentTree } from '../types/component.types';
import { kebabToPascalCase } from '../utils/naming-utils';

export class ImportGenerator {
  // 存储基础导入语句
  private baseImports = new Set<string>();

  // 存储组件导入映射：模块路径 -> 组件名称集合
  private componentImports = new Map<string, Set<string>>();

  constructor() {
    // 添加基础导入
    this.addBaseImport("import React from 'react';");
    this.addBaseImport("import Taro from '@tarojs/taro';");
    this.addBaseImport("import { initComponents, useReactiveComponent } from '@lyy/core';");
  }

  // 添加基础导入语句
  addBaseImport(importStatement: string) {
    this.baseImports.add(importStatement);
  }

  // 为组件树生成导入
  generateForComponentTree(tree: ParsedComponentTree) {
    this.addComponentImport(tree.compName);

    if (tree.children && tree.children.length > 0) {
      tree.children.forEach(child => {
        this.generateForComponentTree(child);
      });
    }
  }

  // 添加组件导入
  private addComponentImport(compName: string) {
    const modulePath = '@lyy/components';
    const componentName = kebabToPascalCase(compName);

    // 获取或创建该模块的组件集合
    let components = this.componentImports.get(modulePath);
    if (!components) {
      components = new Set<string>();
      this.componentImports.set(modulePath, components);
    }

    // 添加组件名称
    components.add(componentName);
  }

  // 生成合并后的导入代码
  generateCode(): string {
    const importLines: string[] = [];

    // 添加基础导入
    this.baseImports.forEach(imp => importLines.push(imp));

    // 添加合并的组件导入
    this.componentImports.forEach((components, modulePath) => {
      if (components.size > 0) {
        const componentList = Array.from(components).join(', ');
        importLines.push(`import { ${componentList} } from '${modulePath}';`);
      }
    });

    return importLines.join('\n');
  }
}
/**
 * 表达式AST处理器
 * 将JSON配置中的表达式转换为可执行的AST代码
 * 集成新架构的表达式引擎
 */

import * as t from '@babel/types';
import { parse } from '@babel/parser';
import traverse from '@babel/traverse';
import { ComponentNode } from '../types/component.types';

// ========== 类型定义 ==========

export interface ExpressionInfo {
  originalExpression: string;
  processedExpression: string;
  dependencies: string[];
  isConditional: boolean;
  isFormula: boolean;
  astNode?: t.Expression;
}

export interface ExpressionContext {
  componentId: string;
  component: ComponentNode;
  propertyPath: string;
}

// ========== 表达式AST处理器 ==========

export class ExpressionASTProcessor {
  private expressionCache: Map<string, ExpressionInfo> = new Map();

  /**
   * 处理对象中的所有表达式
   */
  public processExpressionsInObject(
    obj: any,
    context: ExpressionContext
  ): { processedObj: any, expressions: ExpressionInfo[] } {
    const expressions: ExpressionInfo[] = [];
    const processedObj = this.deepProcessObject(obj, context, expressions);
    
    return { processedObj, expressions };
  }

  /**
   * 生成表达式处理的AST代码
   */
  public generateExpressionHandlerAST(
    expressions: ExpressionInfo[],
    componentId: string
  ): t.Statement[] {
    const handlers: t.Statement[] = [];

    expressions.forEach((expr, index) => {
      const handlerName = `evaluate_${componentId}_${index}`;
      
      // 生成表达式处理函数
      const handlerFunction = t.variableDeclaration('const', [
        t.variableDeclarator(
          t.identifier(handlerName),
          t.callExpression(
            t.identifier('useMemo'),
            [
              t.arrowFunctionExpression(
                [],
                t.blockStatement([
                  t.returnStatement(
                    this.generateExpressionEvaluation(expr, componentId)
                  )
                ])
              ),
              t.arrayExpression(
                expr.dependencies.map(dep => t.identifier(dep))
              )
            ]
          )
        )
      ]);

      handlers.push(handlerFunction);
    });

    return handlers;
  }

  /**
   * 生成条件渲染的AST
   */
  public generateConditionalRenderingAST(
    condition: string,
    componentId: string,
    children: t.JSXElement[]
  ): t.JSXExpressionContainer {
    const processedCondition = this.processExpression(condition, {
      componentId,
      component: {} as ComponentNode,
      propertyPath: 'show.exp'
    });

    return t.jsxExpressionContainer(
      t.conditionalExpression(
        this.generateExpressionEvaluation(processedCondition, componentId),
        t.jsxFragment(
          t.jsxOpeningFragment(),
          t.jsxClosingFragment(),
          children
        ),
        t.nullLiteral()
      )
    );
  }

  /**
   * 生成表单值绑定的AST
   */
  public generateFormValueBindingAST(
    formId: string,
    fieldName: string
  ): t.JSXExpressionContainer {
    return t.jsxExpressionContainer(
      t.memberExpression(
        t.memberExpression(
          t.identifier(`${formId}_modelValue`),
          t.identifier(fieldName)
        ),
        t.identifier('value')
      )
    );
  }

  // ========== 私有方法 ==========

  /**
   * 深度处理对象中的表达式
   */
  private deepProcessObject(
    obj: any,
    context: ExpressionContext,
    expressions: ExpressionInfo[]
  ): any {
    if (typeof obj === 'string') {
      return this.processStringValue(obj, context, expressions);
    }

    if (Array.isArray(obj)) {
      return obj.map(item => 
        this.deepProcessObject(item, context, expressions)
      );
    }

    if (typeof obj === 'object' && obj !== null) {
      const processed: any = {};
      Object.keys(obj).forEach(key => {
        const newContext = {
          ...context,
          propertyPath: context.propertyPath ? `${context.propertyPath}.${key}` : key
        };
        processed[key] = this.deepProcessObject(obj[key], newContext, expressions);
      });
      return processed;
    }

    return obj;
  }

  /**
   * 处理字符串值中的表达式
   */
  private processStringValue(
    value: string,
    context: ExpressionContext,
    expressions: ExpressionInfo[]
  ): string {
    if (!this.containsExpression(value)) {
      return value;
    }

    const expressionInfo = this.processExpression(value, context);
    expressions.push(expressionInfo);

    // 返回表达式处理函数的调用
    const functionName = `evaluate_${context.componentId}_${expressions.length - 1}`;
    return `{${functionName}}`;
  }

  /**
   * 检查字符串是否包含表达式
   */
  private containsExpression(value: string): boolean {
    return typeof value === 'string' && 
           (value.includes('{{') && value.includes('}}') || 
            value.includes('.modelValue.') ||
            this.isFormulaExpression(value));
  }

  /**
   * 处理单个表达式
   */
  private processExpression(
    expression: string,
    context: ExpressionContext
  ): ExpressionInfo {
    const cacheKey = `${context.componentId}_${expression}`;
    
    if (this.expressionCache.has(cacheKey)) {
      return this.expressionCache.get(cacheKey)!;
    }

    const info: ExpressionInfo = {
      originalExpression: expression,
      processedExpression: this.preprocessExpression(expression),
      dependencies: this.extractDependencies(expression, context),
      isConditional: this.isConditionalExpression(expression),
      isFormula: this.isFormulaExpression(expression)
    };

    // 生成AST节点
    try {
      info.astNode = this.parseExpressionToAST(info.processedExpression);
    } catch (error) {
      console.warn(`表达式AST解析失败: ${expression}`, error);
    }

    this.expressionCache.set(cacheKey, info);
    return info;
  }

  /**
   * 预处理表达式
   */
  private preprocessExpression(expression: string): string {
    // 处理 {{...}} 语法
    let processed = expression.replace(/\{\{([^}]+)\}\}/g, '$1');
    
    // 处理组件路径引用 (如: ccuz8ypotl.modelValue.price)
    processed = processed.replace(
      /([a-zA-Z_][a-zA-Z0-9_]*)\.(modelValue|prop|state)\.([a-zA-Z_][a-zA-Z0-9_]*)/g,
      '$1_$2.$3'
    );

    return processed;
  }

  /**
   * 提取表达式依赖
   */
  private extractDependencies(
    expression: string,
    context: ExpressionContext
  ): string[] {
    const dependencies: string[] = [];
    
    // 匹配组件引用模式
    const componentRefPattern = /([a-zA-Z_][a-zA-Z0-9_]*)\.(modelValue|prop|state)/g;
    let match;
    
    while ((match = componentRefPattern.exec(expression)) !== null) {
      const componentId = match[1];
      const stateType = match[2];
      dependencies.push(`${componentId}_${stateType}`);
    }

    // 添加当前组件依赖
    if (dependencies.length === 0) {
      dependencies.push(`${context.componentId}_snapshot`);
    }

    return [...new Set(dependencies)];
  }

  /**
   * 检查是否为条件表达式
   */
  private isConditionalExpression(expression: string): boolean {
    return /[><=!]==?|&&|\|\||\?.*:/.test(expression);
  }

  /**
   * 检查是否为计算公式
   */
  private isFormulaExpression(expression: string): boolean {
    return /[\+\-\*\/\%]/.test(expression) && 
           /modelValue|prop|state/.test(expression);
  }

  /**
   * 将表达式解析为AST
   */
  private parseExpressionToAST(expression: string): t.Expression {
    try {
      const ast = parse(expression, {
        sourceType: 'module',
        plugins: ['jsx', 'typescript']
      });

      let expressionNode: t.Expression | null = null;

      traverse(ast, {
        ExpressionStatement(path) {
          if (!expressionNode) {
            expressionNode = path.node.expression;
          }
        }
      });

      return expressionNode || t.stringLiteral(expression);
    } catch (error) {
      return t.stringLiteral(expression);
    }
  }

  /**
   * 生成表达式求值AST
   */
  private generateExpressionEvaluation(
    expressionInfo: ExpressionInfo,
    componentId: string
  ): t.Expression {
    // 使用表达式引擎进行求值
    return t.callExpression(
      t.identifier('evaluateExpression'),
      [
        t.stringLiteral(expressionInfo.originalExpression),
        t.objectExpression([
          t.objectProperty(
            t.identifier('currentComponent'),
            t.identifier(`${componentId}_component`)
          ),
          t.objectProperty(
            t.identifier('extraContext'),
            t.objectExpression([
              t.objectProperty(
                t.identifier('componentId'),
                t.stringLiteral(componentId)
              )
            ])
          )
        ])
      ]
    );
  }

  /**
   * 清理缓存
   */
  public clearCache(): void {
    this.expressionCache.clear();
  }
}

export default ExpressionASTProcessor;
import { ParsedComponentTree } from '../types/component.types';
import { kebabToPascalCase } from '../utils/naming-utils';

export class ComponentGenerator {
  generateComponent(tree: ParsedComponentTree, indentLevel: number = 0): string {
    const indent = '  '.repeat(indentLevel);
    const componentName = kebabToPascalCase(tree.compName);

    // 生成属性字符串
    const propsString = this.generatePropsString(tree);

    // 生成子组件
    let childrenString = '';
    if (tree.children && tree.children.length > 0) {
      childrenString = tree.children
        .map(child => this.generateComponent(child, indentLevel + 1))
        .join('\n');
    }

    return `${indent}<${componentName} ${propsString} prop={${tree.entityId}.prop}>
${childrenString}
${indent}</${componentName}>`;
  }

  private generatePropsString(tree: ParsedComponentTree): string {
    const attr = {
      entityId: tree.entityId,
      compName: tree.compName,
      compNameCn: tree.compNameCn
      // prop: storeProps?.prop,
    }

    return Object.entries(attr).map(([key, value]) => {
      if (typeof value === 'string') {
        return `${key}="${value}"`;
      } else if (typeof value === 'boolean') {
        return `${key}={${value}}`;
      } else if (typeof value === 'object') {
        return `${key}={${JSON.stringify(value)}}`;
      } else {
        return `${key}={${value}}`;
      }
    })
    .join(' ');

    // return Object.entries({
    //   ...storeProps,
    //   // 特殊处理事件属性
    //   // ...this.generateEventProps(tree.eventHandlers)
    // })
    //   .filter(([key]) => key !== 'actions')
    //   .map(([key, value]) => {
    //     if (typeof value === 'string') {
    //       return `${key}="${value}"`;
    //     } else if (typeof value === 'boolean') {
    //       return `${key}={${value}}`;
    //     } else if (typeof value === 'object') {
    //       return `${key}={${JSON.stringify(value)}}`;
    //     } else {
    //       return `${key}={${value}}`;
    //     }
    //   })
    //   .join(' ');
  }

  private generateEventProps(actions = []) {
    // return actions.reduce((props, action) => {
    //   const eventName = this.mapEventName(action.event);
    //   if (eventName) {
    //     props[eventName] = `handle_${action.entityId}_${action.event}`;
    //   }
    //   return props;
    // }, {});
  }

  private mapEventName(event) {
    const eventMap = {
      'click': 'onClick',
      'beforeMount': 'onBeforeMount',
      'submit': 'onSubmit'
    };
    return eventMap[event] || null;
  }
}
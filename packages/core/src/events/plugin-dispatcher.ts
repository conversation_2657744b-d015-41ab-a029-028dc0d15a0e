/**
 * 插件化事件调度器
 * 与现有EventDispatcher协作，支持插件事件处理
 */

import { eventRegistry, type EventPlugin, type EventContext } from './plugin-system';
import { rootStore } from '../store/root-store';

// ========== 类型定义 ==========

/**
 * 插件执行结果
 */
export interface ExecutionResult<T = any> {
  /** 执行是否成功 */
  success: boolean;
  /** 返回数据 */
  data?: T;
  /** 错误信息 */
  error?: string;
  /** 执行时间(ms) */
  duration: number;
  /** 插件版本 */
  pluginVersion: string;
}

// ========== 插件调度器 ==========

/**
 * 插件事件调度器
 */
export class PluginEventDispatcher {
  private executionQueue = new Map<string, Promise<any>>();

  /**
   * 执行插件事件
   */
  async executePluginEvent<T = any, R = any>(
    type: string, 
    params: T, 
    entityId?: string
  ): Promise<ExecutionResult<R>> {
    const startTime = Date.now();
    
    try {
      // 1. 获取插件
      const plugin = eventRegistry.get(type);
      if (!plugin) {
        return {
          success: false,
          error: `插件 '${type}' 不存在`,
          duration: Date.now() - startTime,
          pluginVersion: 'unknown'
        };
      }

      // 2. 参数验证
      const validation = this.validateParams(params, plugin);
      if (!validation.valid) {
        return {
          success: false,
          error: `参数验证失败: ${validation.errors.join(', ')}`,
          duration: Date.now() - startTime,
          pluginVersion: plugin.version
        };
      }

      // 3. 权限检查
      const permission = this.checkPermissions(plugin, entityId);
      if (!permission.allowed) {
        return {
          success: false,
          error: `权限不足: ${permission.reason}`,
          duration: Date.now() - startTime,
          pluginVersion: plugin.version
        };
      }

      // 4. 创建执行上下文
      const context = this.createEventContext(entityId, plugin);

      // 5. 执行前钩子
      if (plugin.hooks?.beforeExecute) {
        await plugin.hooks.beforeExecute(params, context);
      }

      // 6. 在安全沙箱中执行插件
      const result = await this.executeInSandbox(plugin, params, context);

      // 7. 执行后钩子
      if (plugin.hooks?.afterExecute) {
        await plugin.hooks.afterExecute(result, context);
      }

      // 8. 更新统计
      const duration = Date.now() - startTime;
      eventRegistry.updateStats(type, true, duration);

      return {
        success: true,
        data: result,
        duration,
        pluginVersion: plugin.version
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const plugin = eventRegistry.get(type);
      
      // 错误处理钩子
      if (plugin?.hooks?.onError) {
        try {
          const context = this.createEventContext(entityId, plugin);
          await plugin.hooks.onError(error, context);
        } catch (hookError) {
          console.error('错误处理钩子执行失败:', hookError);
        }
      }

      // 更新统计
      if (plugin) {
        eventRegistry.updateStats(type, false, duration);
      }

      return {
        success: false,
        error: error.message || String(error),
        duration,
        pluginVersion: plugin?.version || 'unknown'
      };
    }
  }

  /**
   * 批量执行插件事件
   */
  async executeBatchPluginEvents(
    events: Array<{ type: string; params: any; entityId?: string }>
  ): Promise<ExecutionResult[]> {
    const results = await Promise.allSettled(
      events.map(event => 
        this.executePluginEvent(event.type, event.params, event.entityId)
      )
    );

    return results.map(result => 
      result.status === 'fulfilled' 
        ? result.value 
        : {
            success: false,
            error: result.reason.message || String(result.reason),
            duration: 0,
            pluginVersion: 'unknown'
          }
    );
  }

  /**
   * 检查插件是否可用
   */
  isPluginAvailable(type: string): boolean {
    return eventRegistry.has(type);
  }

  /**
   * 获取所有可用插件类型
   */
  getAvailablePluginTypes(): string[] {
    return eventRegistry.list().map(plugin => plugin.type);
  }

  // ========== 私有方法 ==========

  /**
   * 验证参数
   */
  private validateParams(params: any, plugin: EventPlugin): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const { required, properties } = plugin.schema;

    // 检查必需参数
    for (const key of required) {
      if (!(key in params) || params[key] === undefined || params[key] === null) {
        errors.push(`缺少必需参数: ${key}`);
      }
    }

    // 检查参数类型
    for (const [key, config] of Object.entries(properties)) {
      if (key in params) {
        const value = params[key];
        const actualType = Array.isArray(value) ? 'array' : typeof value;
        
        if (actualType !== config.type) {
          errors.push(`参数 ${key} 类型错误: 期望 ${config.type}, 实际 ${actualType}`);
        }

        // 枚举值检查
        if (config.enum && !config.enum.includes(value)) {
          errors.push(`参数 ${key} 值无效: 必须是 ${config.enum.join(', ')} 之一`);
        }

        // 自定义验证
        if (config.validation) {
          const validationResult = config.validation(value);
          if (validationResult !== true) {
            errors.push(`参数 ${key} 验证失败: ${validationResult}`);
          }
        }
      }
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * 检查权限
   */
  private checkPermissions(plugin: EventPlugin, entityId?: string): { allowed: boolean; reason?: string } {
    const { permissions, requiresConfirmation } = plugin.security;

    // 检查写入权限
    if (permissions.includes('write:state') && !entityId) {
      return { 
        allowed: false, 
        reason: '写入状态需要指定entityId' 
      };
    }

    // 检查网络权限 (在生产环境可能需要限制)
    if (permissions.includes('network:request') && !plugin.metadata.builtin) {
      if (process.env.NODE_ENV === 'production') {
        return { 
          allowed: false, 
          reason: '生产环境不允许第三方插件进行网络请求' 
        };
      }
    }

    // 检查代码执行权限
    if (permissions.includes('eval:code') && !plugin.metadata.builtin) {
      return { 
        allowed: false, 
        reason: '只有内置插件可以执行代码' 
      };
    }

    return { allowed: true };
  }

  /**
   * 创建事件上下文
   */
  private createEventContext(entityId?: string, plugin?: EventPlugin): EventContext {
    return {
      entityId,
      state: Object.freeze(this.getStateSnapshot()),
      utils: {
        getValue: (path: string) => {
          // 从rootStore获取值
          return this.getValueByPath(path);
        },
        setValue: (path: string, value: any) => {
          // 检查权限
          if (!plugin?.security.permissions.includes('write:state')) {
            throw new Error('插件没有写入状态的权限');
          }
          // this.setValueByPath(path, value);
        },
        getComponent: (id: string) => {
          const component = rootStore.page.components[id];
          return component ? Object.freeze({ ...component }) : undefined;
        },
        updateComponent: (id: string, updates: Record<string, any>) => {
          if (!plugin?.security.permissions.includes('write:state')) {
            throw new Error('插件没有更新组件的权限');
          }
          
          const component = rootStore.page.components[id];
          if (component) {
            Object.keys(updates).forEach(path => {
              const pathArray = path.split('.');
              let target = component;
              
              for (let i = 0; i < pathArray.length - 1; i++) {
                const key = pathArray[i];
                if (!target[key]) target[key] = {};
                target = target[key];
              }
              
              target[pathArray[pathArray.length - 1]] = updates[path];
            });
          }
        }
      },
      emitEvent: async (type: string, params: any) => {
        // 递归调用插件事件
        const result = await this.executePluginEvent(type, params, entityId);
        if (!result.success) {
          throw new Error(result.error);
        }
        return result.data;
      },
      abort: (reason: string) => {
        throw new Error(`执行中止: ${reason}`);
      }
    };
  }

  /**
   * 在沙箱中执行插件
   */
  private async executeInSandbox<T, R>(
    plugin: EventPlugin<T, R>, 
    params: T, 
    context: EventContext
  ): Promise<R> {
    const { timeout = 10000 } = plugin.security;

    // 创建执行Promise
    const executionPromise = Promise.resolve(plugin.handler(params, context));

    // 超时控制
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(`插件执行超时 (${timeout}ms)`)), timeout);
    });

    // 执行并返回结果
    return Promise.race([executionPromise, timeoutPromise]);
  }

  /**
   * 获取状态快照
   */
  private getStateSnapshot(): any {
    try {
      return JSON.parse(JSON.stringify(rootStore));
    } catch (error) {
      console.error('获取状态快照失败:', error);
      return {};
    }
  }

  /**
   * 根据路径获取值
   */
  private getValueByPath(path: string): any {
    const pathArray = path.split('.');
    let current = rootStore;
    
    for (const key of pathArray) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return undefined;
      }
    }
    
    return current;
  }

  /**
   * 根据路径设置值
   */
  private setValueByPath(path: string, value: any): void {
    const pathArray = path.split('.');
    let current = rootStore;
    
    for (let i = 0; i < pathArray.length - 1; i++) {
      const key = pathArray[i];
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }
    
    const finalKey = pathArray[pathArray.length - 1];
    current[finalKey] = value;
  }
}

// ========== 导出单例 ==========

export const pluginDispatcher = new PluginEventDispatcher();

export default pluginDispatcher;
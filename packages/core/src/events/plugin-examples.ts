/**
 * 插件系统使用示例
 * 演示如何创建、注册和使用自定义插件
 */

import type { EventPlugin } from './plugin-system';
import { pluginManager } from './plugin-manager';

// ========== 自定义插件示例 ==========

/**
 * 示例1: 数据转换插件
 */
export const transformDataPlugin: EventPlugin = {
  type: 'transformData',
  name: '数据转换',
  version: '1.0.0',
  handler: async (params: any, context) => {
    const { data, transformType, options = {} } = params;
    
    switch (transformType) {
      case 'toUpperCase':
        return typeof data === 'string' ? data.toUpperCase() : String(data).toUpperCase();
      case 'toLowerCase':
        return typeof data === 'string' ? data.toLowerCase() : String(data).toLowerCase();
      case 'formatNumber':
        const num = Number(data);
        if (isNaN(num)) throw new Error('无效的数字');
        return num.toLocaleString('zh-CN', options);
      case 'formatDate':
        const date = new Date(data);
        if (isNaN(date.getTime())) throw new Error('无效的日期');
        return date.toLocaleDateString('zh-CN', options);
      case 'jsonStringify':
        return JSON.stringify(data, null, options.indent || 0);
      case 'jsonParse':
        return JSON.parse(String(data));
      default:
        throw new Error(`不支持的转换类型: ${transformType}`);
    }
  },
  schema: {
    required: ['data', 'transformType'],
    properties: {
      data: { type: 'string', description: '要转换的数据' },
      transformType: {
        type: 'string',
        description: '转换类型',
        enum: ['toUpperCase', 'toLowerCase', 'formatNumber', 'formatDate', 'jsonStringify', 'jsonParse']
      },
      options: { type: 'object', description: '转换选项', default: {} }
    },
    examples: [
      {
        description: '格式化数字',
        params: {
          data: '1234567',
          transformType: 'formatNumber',
          options: { minimumFractionDigits: 2 }
        }
      }
    ]
  },
  metadata: {
    id: 'custom.transform-data',
    displayName: '数据转换器',
    description: '对数据进行各种格式转换',
    author: { name: 'Developer', email: '<EMAIL>' },
    category: 'utility',
    tags: ['transform', 'format', 'data'],
    docs: 'https://example.com/docs/transform-data'
  },
  security: {
    permissions: ['read:state'],
    timeout: 5000
  }
};

/**
 * 示例2: 计算器插件
 */
export const calculatorPlugin: EventPlugin = {
  type: 'calculate',
  name: '计算器',
  version: '1.2.0',
  handler: async (params: any, context) => {
    const { expression, variables = {} } = params;
    
    // 简单的数学表达式计算器
    try {
      // 替换变量
      let expr = expression;
      Object.keys(variables).forEach(varName => {
        const regex = new RegExp(`\\b${varName}\\b`, 'g');
        expr = expr.replace(regex, variables[varName]);
      });
      
      // 安全的表达式计算 (避免eval)
      const result = evaluateExpression(expr);
      
      return {
        expression: expression,
        resolvedExpression: expr,
        result: result
      };
    } catch (error) {
      throw new Error(`计算错误: ${error.message}`);
    }
  },
  schema: {
    required: ['expression'],
    properties: {
      expression: { type: 'string', description: '数学表达式，如 "a + b * 2"' },
      variables: { type: 'object', description: '变量值映射', default: {} }
    },
    examples: [
      {
        description: '基础计算',
        params: {
          expression: '(price * quantity) + tax',
          variables: { price: 100, quantity: 2, tax: 20 }
        }
      }
    ]
  },
  metadata: {
    id: 'custom.calculator',
    displayName: '表达式计算器',
    description: '计算数学表达式，支持变量替换',
    author: { name: 'Math Team' },
    category: 'utility',
    tags: ['math', 'calculate', 'expression'],
    builtin: false
  },
  security: {
    permissions: [],
    timeout: 3000
  },
  hooks: {
    beforeExecute: async (params, context) => {
      console.log(`🧮 开始计算表达式: ${params.expression}`);
    },
    afterExecute: async (result, context) => {
      console.log(`✅ 计算完成: ${result.result}`);
    }
  }
};

/**
 * 示例3: 条件执行插件
 */
export const conditionalPlugin: EventPlugin = {
  type: 'conditional',
  name: '条件执行',
  version: '1.0.0',
  handler: async (params: any, context) => {
    const { condition, trueAction, falseAction } = params;
    
    // 计算条件
    const conditionResult = await evaluateCondition(condition, context);
    
    // 根据条件执行相应动作
    const actionToExecute = conditionResult ? trueAction : falseAction;
    
    if (actionToExecute) {
      const result = await context.emitEvent(actionToExecute.type, actionToExecute.params);
      return {
        conditionResult,
        executedAction: actionToExecute.type,
        result
      };
    }
    
    return {
      conditionResult,
      executedAction: null,
      result: null
    };
  },
  schema: {
    required: ['condition'],
    properties: {
      condition: { 
        type: 'string', 
        description: '条件表达式，如 "{{state.value}} > 100"'
      },
      trueAction: { 
        type: 'object', 
        description: '条件为真时执行的动作'
      },
      falseAction: { 
        type: 'object', 
        description: '条件为假时执行的动作'
      }
    },
    examples: [
      {
        description: '根据表单值显示不同消息',
        params: {
          condition: '{{component.state.value}} !== ""',
          trueAction: {
            type: 'setValue',
            params: { path: 'prop.text', value: '感谢您的输入' }
          },
          falseAction: {
            type: 'setValue',
            params: { path: 'prop.text', value: '请输入内容' }
          }
        }
      }
    ]
  },
  dependencies: ['setValue'], // 依赖setValue插件
  metadata: {
    id: 'custom.conditional',
    displayName: '条件执行器',
    description: '根据条件执行不同的动作',
    author: { name: 'Logic Team' },
    category: 'action',
    tags: ['condition', 'if-else', 'logic']
  },
  security: {
    permissions: ['read:state', 'write:state'],
    timeout: 8000
  }
};

// ========== 工具函数 ==========

/**
 * 安全的表达式计算
 */
function evaluateExpression(expression: string): number {
  // 简化的数学表达式计算器，实际项目中应使用更安全的解析器
  const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '');
  
  try {
    // 这里使用eval仅作示例，实际应用中应该使用数学表达式解析库
    return Function(`"use strict"; return (${sanitized})`)();
  } catch (error) {
    throw new Error(`表达式计算失败: ${expression}`);
  }
}

/**
 * 条件表达式计算
 */
async function evaluateCondition(condition: string, context: any): Promise<boolean> {
  try {
    // 简化的条件计算，实际应该使用表达式引擎
    let expr = condition;
    
    // 替换上下文变量
    expr = expr.replace(/\{\{([^}]+)\}\}/g, (match, path) => {
      const value = getValueFromPath(context.state, path.trim());
      return JSON.stringify(value);
    });
    
    // 计算条件结果
    return Function(`"use strict"; return Boolean(${expr})`)();
  } catch (error) {
    console.warn(`条件计算失败: ${condition}`, error);
    return false;
  }
}

/**
 * 从路径获取值
 */
function getValueFromPath(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

// ========== 示例使用函数 ==========

/**
 * 演示插件系统使用
 */
export async function demonstratePluginSystem(): Promise<void> {
  try {
    console.log('🎭 插件系统演示开始...\n');
    
    // 1. 初始化插件管理器
    console.log('1️⃣ 初始化插件系统');
    await pluginManager.initialize();
    
    // 2. 加载自定义插件
    console.log('2️⃣ 加载自定义插件');
    await pluginManager.loadPlugin(transformDataPlugin);
    await pluginManager.loadPlugin(calculatorPlugin);
    await pluginManager.loadPlugin(conditionalPlugin);
    
    // 3. 查看插件统计
    console.log('3️⃣ 插件统计信息');
    const stats = pluginManager.getStats();
    console.log(`总插件数: ${stats.totalPlugins}`);
    console.log(`内置插件: ${stats.builtinPlugins}`);
    console.log(`自定义插件: ${stats.customPlugins}`);
    console.log('分类统计:', stats.categories);
    
    // 4. 执行插件事件
    console.log('\n4️⃣ 执行插件事件');
    
    // 数据转换示例
    const transformResult = await pluginManager.executeEvent('transformData', {
      data: '1234567.89',
      transformType: 'formatNumber',
      options: { minimumFractionDigits: 2 }
    });
    console.log('数据转换结果:', transformResult.data);
    
    // 计算器示例
    const calcResult = await pluginManager.executeEvent('calculate', {
      expression: '(price * quantity) * (1 + taxRate)',
      variables: { price: 99.99, quantity: 3, taxRate: 0.08 }
    });
    console.log('计算结果:', calcResult.data);
    
    // 5. 查看执行统计
    console.log('\n5️⃣ 执行统计');
    const pluginStats = pluginManager.getPluginStats();
    console.log('插件执行统计:', pluginStats);
    
    console.log('\n✅ 插件系统演示完成!');
    
  } catch (error) {
    console.error('❌ 演示过程中出现错误:', error);
  }
}

// ========== 导出 ==========

export const EXAMPLE_PLUGINS = [
  transformDataPlugin,
  calculatorPlugin,
  conditionalPlugin
];

export default {
  transformDataPlugin,
  calculatorPlugin,
  conditionalPlugin,
  demonstratePluginSystem
};
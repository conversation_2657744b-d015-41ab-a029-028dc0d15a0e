import { rootStore, globalActions, pageActions } from '../store/root-store';
import { evaluateExpression } from '../utils/expression-engine';
import { ComponentNode } from '../types/component.types';

/**
 * 事件系统 - 系统的"神经中枢"
 * 基于Gemini架构建议实现单向数据流：
 * UI Event → Dispatcher → Action Handler → State Mutation → UI Update
 */

// ========== 类型定义 ==========

// 动作配置接口 (基于docs/event.md)
export interface ActionConfig {
  type: ActionType;
  params: ActionParams;
  condition?: {
    exp?: string;
  };
  thenActions?: ActionConfig[];
}

// 支持的动作类型 (17种)
export type ActionType =
  | 'request'              // 数据请求
  | 'setValue'             // 设置值  
  | 'validate'             // 表单验证
  | 'linkto'               // 页面跳转
  | 'broadcast'            // 广播事件
  | 'copyToClipboard'      // 复制到剪贴板
  | 'openModal'            // 打开模态框
  | 'closeModal'           // 关闭模态框  
  | 'upload'               // 文件上传
  | 'changeLanguage'       // 切换语言
  | 'customerJS'           // 执行自定义JS
  | 'openConfirm'          // 打开确认框
  | 'qtTrackAction'        // QT埋点事件
  | 'requestDataset'       // 数据集请求
  | 'resetValue'           // 重置表单值
  | 'scanCode'             // 扫码
  | 'depAction';           // 依赖动作

// 动作参数接口 (根据不同类型有不同结构)
export interface ActionParams {
  [key: string]: any;
}

// 事件上下文
export interface EventContext {
  currentComponent?: ComponentNode;
  triggerEvent?: string;
  extraData?: any;
}

// 动作执行结果
export interface ActionResult {
  success: boolean;
  data?: any;
  error?: Error;
  message?: string;
}

// ========== 事件分发器 ==========

export class EventDispatcher {
  private static instance: EventDispatcher;
  private isExecuting = false;
  private executionQueue: Array<() => Promise<void>> = [];

  private constructor() {}

  public static getInstance(): EventDispatcher {
    if (!EventDispatcher.instance) {
      EventDispatcher.instance = new EventDispatcher();
    }
    return EventDispatcher.instance;
  }

  /**
   * 分发事件动作序列
   * @param actions 动作配置数组
   * @param context 执行上下文
   * @returns 执行结果
   */
  async dispatch(
    actions: ActionConfig | ActionConfig[],
    context: EventContext = {}
  ): Promise<ActionResult[]> {
    const actionArray = Array.isArray(actions) ? actions : [actions];
    const results: ActionResult[] = [];

    console.log(`开始执行 ${actionArray.length} 个动作`, actionArray);

    try {
      for (const action of actionArray) {
        // 1. 条件判断
        if (action.condition?.exp) {
          const conditionResult = evaluateExpression(action.condition.exp, {
            currentComponent: context.currentComponent,
            extraContext: { context }
          });
          
          if (!conditionResult) {
            console.log(`动作 ${action.type} 条件不满足，跳过执行`);
            results.push({
              success: true,
              message: '条件不满足，跳过执行'
            });
            continue;
          }
        }

        // 2. 执行主要动作
        const result = await this.executeAction(action, context);
        results.push(result);

        // 3. 如果执行失败，是否继续执行后续动作？
        if (!result.success) {
          console.error(`动作 ${action.type} 执行失败:`, result.error);
          // 这里可以根据配置决定是否继续
        }

        // 4. 执行后续动作 (thenActions)
        if (action.thenActions && action.thenActions.length > 0) {
          console.log(`执行 ${action.thenActions.length} 个后续动作`);
          const thenResults = await this.dispatch(action.thenActions, {
            ...context,
            // 可以传递上一个动作的结果
            extraData: { ...context.extraData, previousResult: result }
          });
          results.push(...thenResults);
        }
      }
    } catch (error) {
      console.error('事件分发过程中发生错误:', error);
      results.push({
        success: false,
        error: error as Error,
        message: '事件分发失败'
      });
    }

    console.log('事件分发完成，结果:', results);
    return results;
  }

  /**
   * 执行单个动作
   */
  private async executeAction(
    action: ActionConfig,
    context: EventContext
  ): Promise<ActionResult> {
    const handler = actionHandlers[action.type];
    
    if (!handler) {
      const error = new Error(`未知的动作类型: ${action.type}`);
      console.error(error);
      return {
        success: false,
        error,
        message: `不支持的动作类型: ${action.type}`
      };
    }

    try {
      console.log(`执行动作 ${action.type}，参数:`, action.params);
      const result = await handler(action.params, context);
      
      return {
        success: true,
        data: result,
        message: `动作 ${action.type} 执行成功`
      };
    } catch (error) {
      console.error(`动作 ${action.type} 执行出错:`, error);
      return {
        success: false,
        error: error as Error,
        message: `动作 ${action.type} 执行失败`
      };
    }
  }

  /**
   * 队列化执行 (防并发冲突)
   */
  async queueExecution(
    actions: ActionConfig | ActionConfig[],
    context: EventContext = {}
  ): Promise<ActionResult[]> {
    return new Promise((resolve) => {
      this.executionQueue.push(async () => {
        const results = await this.dispatch(actions, context);
        resolve(results);
      });

      this.processQueue();
    });
  }

  private async processQueue() {
    if (this.isExecuting || this.executionQueue.length === 0) {
      return;
    }

    this.isExecuting = true;
    
    while (this.executionQueue.length > 0) {
      const task = this.executionQueue.shift();
      if (task) {
        await task();
      }
    }

    this.isExecuting = false;
  }
}

// ========== 动作处理器映射表 ==========

// 动作处理器函数类型
type ActionHandler = (params: ActionParams, context: EventContext) => Promise<any> | any;

/**
 * 17种事件类型的处理器映射表
 * 每个处理器都是纯函数，接收参数和上下文，返回结果
 */
const actionHandlers: Record<ActionType, ActionHandler> = {
  /**
   * 1. request - 数据请求
   * 最常用的事件类型，支持各种HTTP请求
   */
  request: async (params: any, context: EventContext) => {
    const {
      url,
      method = 'GET',
      responseType = 'json',
      payloads,
      responseDataKey,
      headerPayloads = [],
      customOption = {}
    } = params;

    // 解析请求参数中的表达式
    const resolvedPayloads = evaluateExpression(payloads, {
      currentComponent: context.currentComponent,
      extraContext: context.extraData
    });

    // 解析请求头中的表达式
    const resolvedHeaders: Record<string, string> = {
      'Content-Type': 'application/json'
    };
    
    headerPayloads.forEach((header: any) => {
      if (header.key && header.value) {
        resolvedHeaders[header.key] = evaluateExpression(header.value, {
          currentComponent: context.currentComponent,
          extraContext: context.extraData
        });
      }
    });

    // 设置loading状态
    if (responseDataKey) {
      globalActions.setApiData(responseDataKey, {
        loading: true,
        data: null,
        error: null
      });
    }

    try {
      console.log(`发起 ${method} 请求:`, url, resolvedPayloads);
      
      const fetchOptions: RequestInit = {
        method,
        headers: resolvedHeaders
      };

      // GET请求参数放在URL中，其他请求放在body中
      let requestUrl = url;
      if (method.toUpperCase() === 'GET') {
        if (resolvedPayloads && typeof resolvedPayloads === 'object') {
          const params = new URLSearchParams();
          Object.keys(resolvedPayloads).forEach(key => {
            params.append(key, String(resolvedPayloads[key]));
          });
          requestUrl += (requestUrl.includes('?') ? '&' : '?') + params.toString();
        }
      } else {
        if (resolvedPayloads) {
          fetchOptions.body = JSON.stringify(resolvedPayloads);
        }
      }

      const response = await fetch(requestUrl, fetchOptions);
      
      let data;
      if (responseType === 'json') {
        data = await response.json();
      } else {
        data = await response.text();
      }

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
      }

      // 更新API数据到全局状态
      if (responseDataKey) {
        globalActions.setApiData(responseDataKey, {
          loading: false,
          data,
          error: null
        });
      }

      console.log('请求成功，响应数据:', data);
      return data;
    } catch (error) {
      console.error('请求失败:', error);
      
      // 更新错误状态
      if (responseDataKey) {
        globalActions.setApiData(responseDataKey, {
          loading: false,
          data: null,
          error: error as Error
        });
      }
      
      throw error;
    }
  },

  /**
   * 2. setValue - 设置值
   * 设置组件或全局状态的值
   */
  setValue: (params: any, context: EventContext) => {
    const { from, to, arrayMergeType } = params;
    
    // 解析源值
    let sourceValue;
    if (from.type === 'static') {
      sourceValue = from.static;
    } else if (from.type === 'dynamic') {
      sourceValue = evaluateExpression(`{{${from.dynamic.nodePath}}}`, {
        currentComponent: context.currentComponent,
        extraContext: context.extraData
      });
    } else if (from.type === 'higher') {
      // 处理复杂的higher类型 (暂时简化)
      sourceValue = evaluateExpression(from.higher, {
        currentComponent: context.currentComponent,
        extraContext: context.extraData
      });
    }

    // 解析目标路径并设置值
    if (to.type === 'dynamic') {
      const targetPath = to.dynamic.nodePath;
      
      // 判断是组件状态还是全局状态
      if (targetPath.startsWith('components.')) {
        // 组件状态路径: components.entityId.prop.field
        const pathParts = targetPath.split('.');
        if (pathParts.length >= 3) {
          const entityId = pathParts[1];
          const propertyPath = pathParts.slice(2).join('.');
          pageActions.updateComponentProp(entityId, propertyPath, sourceValue);
        }
      } else if (targetPath.startsWith('global.')) {
        // 全局状态路径: global.commonData.key
        const pathParts = targetPath.split('.');
        if (pathParts.length >= 3 && pathParts[1] === 'commonData') {
          const key = pathParts.slice(2).join('.');
          globalActions.setCommonData(key, sourceValue);
        }
      }
    }

    console.log('setValue执行成功:', { from, to, value: sourceValue });
    return sourceValue;
  },

  /**
   * 3. validate - 表单验证
   * 验证指定表单或字段
   */
  validate: async (params: any, context: EventContext) => {
    const { targetId, fields } = params;
    
    console.log('执行表单验证:', { targetId, fields });
    
    // 获取目标组件
    const targetComponent = rootStore.page.components[targetId];
    if (!targetComponent) {
      throw new Error(`验证目标组件不存在: ${targetId}`);
    }

    // 简化的验证逻辑 (实际项目中会更复杂)
    const validationResults = {
      isValid: true,
      errors: [] as string[]
    };

    // 如果指定了字段，只验证指定字段
    if (fields && Array.isArray(fields)) {
      fields.forEach((field: string) => {
        const value = targetComponent.state?.[field];
        if (!value || (typeof value === 'string' && value.trim() === '')) {
          validationResults.isValid = false;
          validationResults.errors.push(`字段 ${field} 不能为空`);
        }
      });
    } else {
      // 验证所有必填字段 (简化版)
      if (targetComponent.prop?.rules) {
        // 这里应该根据实际的验证规则来实现
        console.log('执行验证规则:', targetComponent.prop.rules);
      }
    }

    // 更新组件验证状态
    pageActions.updateComponentProp(targetId, 'validationResult', validationResults);
    
    if (!validationResults.isValid) {
      throw new Error(`表单验证失败: ${validationResults.errors.join(', ')}`);
    }

    console.log('表单验证通过');
    return validationResults;
  },

  /**
   * 4. linkto - 页面跳转
   * Taro页面导航
   */
  linkto: (params: any, context: EventContext) => {
    const { url, type = 'navigateTo', payloads, isAddRandom = false } = params;
    
    // 解析参数
    const resolvedPayloads = evaluateExpression(payloads, {
      currentComponent: context.currentComponent,
      extraContext: context.extraData
    });

    let finalUrl = url;
    
    // 添加查询参数
    if (resolvedPayloads && typeof resolvedPayloads === 'object') {
      const params = new URLSearchParams();
      Object.keys(resolvedPayloads).forEach(key => {
        params.append(key, String(resolvedPayloads[key]));
      });
      finalUrl += (finalUrl.includes('?') ? '&' : '?') + params.toString();
    }

    // 添加随机参数防缓存
    if (isAddRandom) {
      finalUrl += (finalUrl.includes('?') ? '&' : '?') + `_t=${Date.now()}`;
    }

    console.log(`执行页面跳转: ${type} -> ${finalUrl}`);

    // 使用Taro的导航API
    const Taro = require('@tarojs/taro');
    
    switch (type) {
      case 'navigateTo':
        return Taro.navigateTo({ url: finalUrl });
      case 'redirectTo':
        return Taro.redirectTo({ url: finalUrl });
      case 'switchTab':
        return Taro.switchTab({ url: finalUrl });
      case 'navigateBack':
        return Taro.navigateBack({ delta: params.go || 1 });
      case 'reLaunch':
        return Taro.reLaunch({ url: finalUrl });
      default:
        throw new Error(`不支持的跳转类型: ${type}`);
    }
  },

  /**
   * 5. broadcast - 广播事件
   * 向指定组件广播事件
   */
  broadcast: async (params: any, context: EventContext) => {
    const { targetId, event } = params;
    
    console.log(`广播事件到组件 ${targetId}:`, event);
    
    // 获取目标组件
    const targetComponent = rootStore.page.components[targetId];
    if (!targetComponent) {
      console.warn(`广播目标组件不存在: ${targetId}`);
      return;
    }

    // 查找目标组件中匹配的事件配置
    const eventConfig = targetComponent.events?.find((e: any) => e.trigger === event);
    if (eventConfig && eventConfig.actions) {
      // 递归执行目标组件的事件动作
      const dispatcher = EventDispatcher.getInstance();
      return await dispatcher.dispatch(eventConfig.actions, {
        currentComponent: targetComponent,
        triggerEvent: event
      });
    } else {
      console.warn(`目标组件 ${targetId} 中没有找到事件 ${event}`);
    }
  },

  // ========== 其余12种事件处理器 - 完整实现 ==========
  
  /**
   * 6. copyToClipboard - 复制到剪贴板
   * 支持复制文本内容到系统剪贴板
   */
  copyToClipboard: async (params: any, context: EventContext) => {
    const { target } = params;
    
    // 解析要复制的内容
    const content = evaluateExpression(target, {
      currentComponent: context.currentComponent,
      extraContext: context.extraData
    });
    
    const textToCopy = String(content || '');
    console.log('复制到剪贴板:', textToCopy);
    
    try {
      // Taro环境下的剪贴板API
      const Taro = require('@tarojs/taro');
      
      await Taro.setClipboardData({
        data: textToCopy
      });
      
      // 显示成功提示
      await Taro.showToast({
        title: '复制成功',
        icon: 'success',
        duration: 1500
      });
      
      return textToCopy;
    } catch (error) {
      console.error('复制失败:', error);
      
      // 备用方案：尝试使用浏览器API (H5环境)
      try {
        if (typeof navigator !== 'undefined' && navigator.clipboard) {
          await navigator.clipboard.writeText(textToCopy);
          return textToCopy;
        }
      } catch (fallbackError) {
        console.error('剪贴板访问失败:', fallbackError);
      }
      
      throw new Error('复制到剪贴板失败');
    }
  },

  /**
   * 7. openModal - 打开模态框
   * 显示指定的模态框组件
   */
  openModal: (params: any, context: EventContext) => {
    const { targetId, modalData, showMask = true, closable = true } = params;
    
    console.log(`打开模态框: ${targetId}`, modalData);
    
    // 更新目标模态框组件的显示状态
    const modalComponent = rootStore.page.components[targetId];
    if (!modalComponent) {
      throw new Error(`模态框组件不存在: ${targetId}`);
    }
    
    // 设置模态框的显示属性
    pageActions.updateComponentProp(targetId, 'prop.visible', true);
    pageActions.updateComponentProp(targetId, 'prop.showMask', showMask);
    pageActions.updateComponentProp(targetId, 'prop.closable', closable);
    
    // 如果传递了数据，设置模态框数据
    if (modalData) {
      const resolvedData = evaluateExpression(modalData, {
        currentComponent: context.currentComponent,
        extraContext: context.extraData
      });
      
      pageActions.updateComponentProp(targetId, 'state.modalData', resolvedData);
    }
    
    // 记录模态框打开状态到UI层
    if (!rootStore.ui.openedModals) {
      (rootStore.ui as any).openedModals = [];
    }
    (rootStore.ui as any).openedModals.push(targetId);
    
    return { modalId: targetId, visible: true };
  },

  /**
   * 8. closeModal - 关闭模态框
   * 隐藏指定的模态框组件
   */
  closeModal: (params: any, context: EventContext) => {
    const { targetId, clearData = false } = params;
    
    console.log(`关闭模态框: ${targetId}`);
    
    // 更新目标模态框组件的显示状态
    const modalComponent = rootStore.page.components[targetId];
    if (!modalComponent) {
      console.warn(`模态框组件不存在: ${targetId}`);
      return;
    }
    
    // 隐藏模态框
    pageActions.updateComponentProp(targetId, 'prop.visible', false);
    
    // 清理模态框数据 (可选)
    if (clearData) {
      pageActions.updateComponentProp(targetId, 'state.modalData', null);
    }
    
    // 更新UI层的打开状态
    if ((rootStore.ui as any).openedModals) {
      const modals = (rootStore.ui as any).openedModals as string[];
      const index = modals.indexOf(targetId);
      if (index > -1) {
        modals.splice(index, 1);
      }
    }
    
    return { modalId: targetId, visible: false };
  },

  /**
   * 9. upload - 文件上传
   * 处理文件上传功能
   */
  upload: async (params: any, context: EventContext) => {
    const { 
      url, 
      filePath,
      name = 'file',
      formData = {},
      header = {},
      maxSize,
      accept = 'image/*',
      multiple = false,
      responseDataKey
    } = params;
    
    console.log('开始文件上传:', { url, name, accept, multiple });
    
    try {
      const Taro = require('@tarojs/taro');
      let uploadResult;
      
      // 如果没有提供文件路径，先选择文件
      if (!filePath) {
        const chooseResult = await Taro.chooseImage({
          count: multiple ? 9 : 1,
          sizeType: ['original', 'compressed'],
          sourceType: ['album', 'camera']
        });
        
        if (!chooseResult.tempFilePaths || chooseResult.tempFilePaths.length === 0) {
          throw new Error('未选择文件');
        }
        
        // 上传所有选中的文件
        const uploadPromises = chooseResult.tempFilePaths.map(async (tempFilePath) => {
          // 检查文件大小
          if (maxSize) {
            try {
              const fileInfo = await Taro.getFileInfo({
                filePath: tempFilePath
              });
              
              if (fileInfo.size > maxSize) {
                throw new Error(`文件大小超出限制: ${fileInfo.size} > ${maxSize}`);
              }
            } catch (sizeError) {
              console.warn('无法获取文件大小信息:', sizeError);
            }
          }
          
          // 解析额外的表单数据
          const resolvedFormData = evaluateExpression(formData, {
            currentComponent: context.currentComponent,
            extraContext: context.extraData
          });
          
          // 解析请求头
          const resolvedHeader = evaluateExpression(header, {
            currentComponent: context.currentComponent, 
            extraContext: context.extraData
          });
          
          // 执行上传
          return await Taro.uploadFile({
            url,
            filePath: tempFilePath,
            name,
            formData: resolvedFormData,
            header: resolvedHeader
          });
        });
        
        uploadResult = await Promise.all(uploadPromises);
      } else {
        // 直接上传指定路径的文件
        const resolvedFormData = evaluateExpression(formData, {
          currentComponent: context.currentComponent,
          extraContext: context.extraData
        });
        
        const resolvedHeader = evaluateExpression(header, {
          currentComponent: context.currentComponent,
          extraContext: context.extraData
        });
        
        uploadResult = [await Taro.uploadFile({
          url,
          filePath,
          name,
          formData: resolvedFormData,
          header: resolvedHeader
        })];
      }
      
      // 处理上传结果
      const results = uploadResult.map((result) => {
        let responseData;
        try {
          responseData = JSON.parse(result.data);
        } catch (parseError) {
          responseData = result.data;
        }
        
        return {
          statusCode: result.statusCode,
          data: responseData,
          success: result.statusCode === 200
        };
      });
      
      // 保存上传结果到全局状态
      if (responseDataKey) {
        globalActions.setApiData(responseDataKey, {
          loading: false,
          data: multiple ? results : results[0],
          error: null
        });
      }
      
      console.log('文件上传完成:', results);
      
      // 显示成功提示
      await Taro.showToast({
        title: '上传成功',
        icon: 'success',
        duration: 1500
      });
      
      return multiple ? results : results[0];
    } catch (error) {
      console.error('文件上传失败:', error);
      
      // 保存错误状态
      if (responseDataKey) {
        globalActions.setApiData(responseDataKey, {
          loading: false,
          data: null,
          error: error as Error
        });
      }
      
      // 显示错误提示
      try {
        const Taro = require('@tarojs/taro');
        await Taro.showToast({
          title: '上传失败',
          icon: 'error',
          duration: 2000
        });
      } catch (toastError) {
        console.error('显示错误提示失败:', toastError);
      }
      
      throw error;
    }
  },

  /**
   * 10. changeLanguage - 切换语言
   * 支持国际化语言切换
   */
  changeLanguage: (params: any, context: EventContext) => {
    const { language, reloadPage = false } = params;
    
    // 解析语言参数
    const targetLanguage = evaluateExpression(language, {
      currentComponent: context.currentComponent,
      extraContext: context.extraData
    });
    
    console.log(`切换语言: ${targetLanguage}`);
    
    // 更新全局环境配置
    globalActions.setApiData('currentLanguage', {
      loading: false,
      data: targetLanguage,
      error: null
    });
    
    rootStore.global.env.language = targetLanguage;
    
    // 保存语言设置到本地存储
    try {
      const Taro = require('@tarojs/taro');
      
      Taro.setStorageSync('app_language', targetLanguage);
      
      // 显示切换成功提示
      Taro.showToast({
        title: `已切换到${getLanguageName(targetLanguage)}`,
        icon: 'success',
        duration: 1500
      });
      
      // 如果需要重新加载页面
      if (reloadPage) {
        setTimeout(() => {
          Taro.reLaunch({
            url: getCurrentPagePath() || '/pages/index/home'
          });
        }, 1000);
      }
      
    } catch (error) {
      console.error('语言切换失败:', error);
    }
    
    return { language: targetLanguage, success: true };
  },

  /**
   * 11. customerJS - 执行自定义JS
   * 安全的自定义JavaScript代码执行
   */
  customerJS: async (params: any, context: EventContext) => {
    const { customJs, sandbox = true, timeout = 5000 } = params;
    
    console.log('执行自定义JS:', customJs);
    
    if (!customJs || typeof customJs !== 'string') {
      throw new Error('自定义JS代码不能为空');
    }
    
    // 创建安全的执行上下文
    const safeContext = {
      // 提供给自定义JS的安全API
      console: {
        log: console.log,
        warn: console.warn,
        error: console.error
      },
      
      // 状态访问API
      getComponentState: (entityId: string) => {
        return rootStore.page.components[entityId];
      },
      
      setComponentState: (entityId: string, propPath: string, value: any) => {
        pageActions.updateComponentProp(entityId, propPath, value);
      },
      
      // 全局数据访问
      getGlobalData: (key: string) => {
        return rootStore.global.commonData[key];
      },
      
      setGlobalData: (key: string, value: any) => {
        globalActions.setCommonData(key, value);
      },
      
      // 表达式计算
      evaluate: (expression: string) => {
        return evaluateExpression(expression, {
          currentComponent: context.currentComponent,
          extraContext: context.extraData
        });
      },
      
      // Taro API (部分安全API)
      showToast: async (options: any) => {
        const Taro = require('@tarojs/taro');
        return await Taro.showToast(options);
      },
      
      showModal: async (options: any) => {
        const Taro = require('@tarojs/taro');
        return await Taro.showModal(options);
      },
      
      // 数学函数
      Math,
      
      // 日期处理
      Date,
      
      // JSON处理
      JSON,
      
      // 当前组件和上下文
      currentComponent: context.currentComponent,
      extraData: context.extraData
    };
    
    try {
      let result;
      
      if (sandbox) {
        // 沙箱模式：使用Function构造器创建隔离环境
        const contextKeys = Object.keys(safeContext);
        const contextValues = Object.values(safeContext);
        
        // 创建超时控制
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('自定义JS执行超时')), timeout);
        });
        
        const executePromise = new Promise((resolve) => {
          const func = new Function(...contextKeys, `
            "use strict";
            try {
              ${customJs}
            } catch (error) {
              throw new Error('自定义JS执行错误: ' + error.message);
            }
          `);
          
          const execResult = func.apply(null, contextValues);
          resolve(execResult);
        });
        
        result = await Promise.race([executePromise, timeoutPromise]);
      } else {
        // 非沙箱模式：直接执行（危险，仅开发环境使用）
        console.warn('⚠️ 非沙箱模式执行自定义JS，存在安全风险');
        
        // 将安全上下文添加到全局
        (globalThis as any).customJSContext = safeContext;
        
        try {
          result = eval(`
            (function() {
              const { 
                console, getComponentState, setComponentState, 
                getGlobalData, setGlobalData, evaluate, showToast, showModal,
                Math, Date, JSON, currentComponent, extraData
              } = globalThis.customJSContext;
              
              ${customJs}
            })()
          `);
        } finally {
          // 清理全局上下文
          delete (globalThis as any).customJSContext;
        }
      }
      
      console.log('自定义JS执行完成，结果:', result);
      return result;
      
    } catch (error) {
      console.error('自定义JS执行失败:', error);
      
      // 显示错误提示
      try {
        const Taro = require('@tarojs/taro');
        await Taro.showModal({
          title: '脚本执行错误',
          content: `${error.message}`,
          showCancel: false
        });
      } catch (modalError) {
        console.error('显示错误弹框失败:', modalError);
      }
      
      throw error;
    }
  },

  /**
   * 12. openConfirm - 打开确认框
   * 显示确认对话框并处理用户选择
   */
  openConfirm: async (params: any, context: EventContext) => {
    const { 
      title = '确认',
      content = '确定要执行此操作吗？',
      confirmText = '确定',
      cancelText = '取消',
      showCancel = true,
      confirmColor = '#3CC51F',
      cancelColor = '#000000',
      confirmActions = [],
      cancelActions = []
    } = params;
    
    console.log('打开确认框:', { title, content });
    
    try {
      const Taro = require('@tarojs/taro');
      
      // 解析动态内容
      const resolvedTitle = evaluateExpression(title, {
        currentComponent: context.currentComponent,
        extraContext: context.extraData
      });
      
      const resolvedContent = evaluateExpression(content, {
        currentComponent: context.currentComponent,
        extraContext: context.extraData
      });
      
      // 显示确认框
      const result = await Taro.showModal({
        title: String(resolvedTitle),
        content: String(resolvedContent),
        showCancel,
        confirmText,
        cancelText,
        confirmColor,
        cancelColor
      });
      
      // 根据用户选择执行对应的动作
      if (result.confirm && confirmActions && confirmActions.length > 0) {
        console.log('用户确认，执行确认动作');
        
        // 递归执行确认动作
        const dispatcher = EventDispatcher.getInstance();
        await dispatcher.dispatch(confirmActions, {
          ...context,
          extraData: { 
            ...context.extraData, 
            confirmResult: result,
            userChoice: 'confirm'
          }
        });
        
        return { action: 'confirm', result };
        
      } else if (result.cancel && cancelActions && cancelActions.length > 0) {
        console.log('用户取消，执行取消动作');
        
        // 递归执行取消动作
        const dispatcher = EventDispatcher.getInstance();
        await dispatcher.dispatch(cancelActions, {
          ...context,
          extraData: { 
            ...context.extraData, 
            confirmResult: result,
            userChoice: 'cancel'
          }
        });
        
        return { action: 'cancel', result };
        
      } else {
        console.log('用户操作完成，无后续动作');
        return { action: result.confirm ? 'confirm' : 'cancel', result };
      }
      
    } catch (error) {
      console.error('确认框显示失败:', error);
      throw error;
    }
  },

  /**
   * 13. qtTrackAction - QT埋点事件
   * 用于数据统计和用户行为分析
   */
  qtTrackAction: async (params: any, context: EventContext) => {
    const { 
      event, 
      properties = {}, 
      userId, 
      sessionId,
      timestamp = Date.now(),
      platform = 'unknown'
    } = params;
    
    console.log('QT埋点事件:', { event, properties });
    
    // 解析事件属性中的表达式
    const resolvedProperties = evaluateExpression(properties, {
      currentComponent: context.currentComponent,
      extraContext: context.extraData
    });
    
    // 构建埋点数据
    const trackData = {
      event: String(event),
      properties: {
        ...resolvedProperties,
        // 自动添加系统信息
        platform: rootStore.global.env.platform || platform,
        device: rootStore.global.env.device,
        language: rootStore.global.env.language,
        timestamp,
        page_path: getCurrentPagePath(),
        component_id: context.currentComponent?.entityId
      },
      userId: userId || rootStore.global.user.id,
      sessionId: sessionId || generateSessionId()
    };
    
    try {
      // 1. 本地存储埋点数据 (离线支持)
      const storageKey = 'qt_track_events';
      const Taro = require('@tarojs/taro');
      
      let storedEvents = [];
      try {
        const stored = Taro.getStorageSync(storageKey);
        storedEvents = Array.isArray(stored) ? stored : [];
      } catch (storageError) {
        console.warn('读取本地埋点数据失败:', storageError);
      }
      
      // 添加新事件
      storedEvents.push(trackData);
      
      // 限制本地存储数量 (最多保留1000条)
      if (storedEvents.length > 1000) {
        storedEvents = storedEvents.slice(-1000);
      }
      
      Taro.setStorageSync(storageKey, storedEvents);
      
      // 2. 实时上报 (如果网络可用)
      try {
        const reportUrl = rootStore.global.commonData.qtTrackUrl || '/api/track';
        
        await fetch(reportUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            events: [trackData]
          })
        });
        
        console.log('埋点数据上报成功');
        
        // 上报成功后，清理本地存储的对应事件
        const currentStored = Taro.getStorageSync(storageKey) || [];
        const filtered = currentStored.filter((item: any) => 
          item.timestamp !== trackData.timestamp || item.event !== trackData.event
        );
        Taro.setStorageSync(storageKey, filtered);
        
      } catch (reportError) {
        console.warn('埋点数据实时上报失败，已保存到本地:', reportError);
      }
      
      // 3. 更新埋点统计到全局状态
      if (!rootStore.global.commonData.trackingStats) {
        rootStore.global.commonData.trackingStats = {};
      }
      
      const stats = rootStore.global.commonData.trackingStats;
      stats[event] = (stats[event] || 0) + 1;
      
      return trackData;
      
    } catch (error) {
      console.error('埋点事件处理失败:', error);
      throw error;
    }
  },

  /**
   * 14. requestDataset - 数据集请求
   * 用于请求预定义的数据集或API集合
   */
  requestDataset: async (params: any, context: EventContext) => {
    const { 
      datasetId, 
      filters = {}, 
      pagination = {},
      responseDataKey,
      cached = true,
      timeout = 10000
    } = params;
    
    console.log('数据集请求:', { datasetId, filters });
    
    // 解析过滤条件
    const resolvedFilters = evaluateExpression(filters, {
      currentComponent: context.currentComponent,
      extraContext: context.extraData
    });
    
    // 构建数据集请求
    const requestConfig = {
      datasetId,
      filters: resolvedFilters,
      pagination,
      timestamp: Date.now()
    };
    
    try {
      // 检查缓存
      const cacheKey = `dataset_${datasetId}_${JSON.stringify(resolvedFilters)}`;
      
      if (cached && rootStore.global.apiData[cacheKey]) {
        const cachedData = rootStore.global.apiData[cacheKey];
        
        // 检查缓存是否过期 (默认5分钟)
        const cacheExpiry = 5 * 60 * 1000; // 5分钟
        if (cachedData.timestamp && (Date.now() - cachedData.timestamp < cacheExpiry)) {
          console.log('使用缓存的数据集数据');
          
          if (responseDataKey && responseDataKey !== cacheKey) {
            globalActions.setApiData(responseDataKey, cachedData);
          }
          
          return cachedData.data;
        }
      }
      
      // 设置加载状态
      if (responseDataKey) {
        globalActions.setApiData(responseDataKey, {
          loading: true,
          data: null,
          error: null
        });
      }
      
      // 构建数据集API请求
      const datasetApiUrl = rootStore.global.commonData.datasetApiUrl || '/api/dataset';
      
      const response = await fetch(`${datasetApiUrl}/${datasetId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${rootStore.global.user.id || 'anonymous'}`
        },
        body: JSON.stringify(requestConfig)
      });
      
      if (!response.ok) {
        throw new Error(`数据集请求失败: ${response.status} ${response.statusText}`);
      }
      
      const responseData = await response.json();
      
      // 数据处理和转换
      const processedData = {
        ...responseData,
        datasetId,
        requestFilters: resolvedFilters,
        totalCount: responseData.total || responseData.data?.length || 0,
        currentPage: pagination.page || 1,
        pageSize: pagination.pageSize || 20
      };
      
      // 保存到缓存和响应数据
      globalActions.setApiData(cacheKey, {
        loading: false,
        data: processedData,
        error: null
      });
      
      if (responseDataKey && responseDataKey !== cacheKey) {
        globalActions.setApiData(responseDataKey, {
          loading: false,
          data: processedData,
          error: null
        });
      }
      
      console.log('数据集请求成功:', processedData);
      return processedData;
      
    } catch (error) {
      console.error('数据集请求失败:', error);
      
      // 更新错误状态
      if (responseDataKey) {
        globalActions.setApiData(responseDataKey, {
          loading: false,
          data: null,
          error: error as Error
        });
      }
      
      throw error;
    }
  },

  /**
   * 15. resetValue - 重置表单值 (完善版)
   * 重置组件或表单的值到默认状态
   */
  resetValue: (params: any, context: EventContext) => {
    const { targetId, fields, defaultValues = {} } = params;
    
    console.log('重置值:', { targetId, fields });
    
    const targetComponent = rootStore.page.components[targetId];
    if (!targetComponent) {
      console.warn(`重置目标组件不存在: ${targetId}`);
      return;
    }
    
    // 解析默认值
    const resolvedDefaults = evaluateExpression(defaultValues, {
      currentComponent: context.currentComponent,
      extraContext: context.extraData
    });
    
    if (fields && Array.isArray(fields)) {
      // 重置指定字段
      fields.forEach((field: string) => {
        const defaultValue = resolvedDefaults[field] !== undefined 
          ? resolvedDefaults[field] 
          : getDefaultValueByType(targetComponent.prop?.type?.[field]);
          
        pageActions.updateComponentProp(targetId, `state.${field}`, defaultValue);
        
        // 同时更新modelValue (如果存在)
        if (targetComponent.modelValue) {
          pageActions.updateComponentProp(targetId, `modelValue.${field}`, defaultValue);
        }
      });
    } else {
      // 重置所有字段
      if (targetComponent.state) {
        Object.keys(targetComponent.state).forEach(key => {
          const defaultValue = resolvedDefaults[key] !== undefined 
            ? resolvedDefaults[key] 
            : getDefaultValueByType(targetComponent.prop?.type?.[key]);
            
          pageActions.updateComponentProp(targetId, `state.${key}`, defaultValue);
        });
      }
      
      // 重置modelValue
      if (targetComponent.modelValue) {
        Object.keys(targetComponent.modelValue).forEach(key => {
          const defaultValue = resolvedDefaults[key] !== undefined 
            ? resolvedDefaults[key] 
            : getDefaultValueByType(targetComponent.prop?.type?.[key]);
            
          pageActions.updateComponentProp(targetId, `modelValue.${key}`, defaultValue);
        });
      }
      
      // 清理验证结果
      pageActions.updateComponentProp(targetId, 'validationResult', null);
    }
    
    console.log(`组件 ${targetId} 值重置完成`);
    return { targetId, success: true };
  },

  /**
   * 16. scanCode - 扫码功能
   * 调用系统相机扫描二维码/条形码
   */
  scanCode: async (params: any, context: EventContext) => {
    const { 
      scanType = ['qrCode', 'barCode'], 
      onlyFromCamera = false,
      responseDataKey,
      autoProcess = true 
    } = params;
    
    console.log('开始扫码:', { scanType, onlyFromCamera });
    
    try {
      const Taro = require('@tarojs/taro');
      
      // 调用扫码API
      const scanResult = await Taro.scanCode({
        scanType,
        onlyFromCamera
      });
      
      console.log('扫码结果:', scanResult);
      
      let processedResult = {
        rawResult: scanResult.result,
        scanType: scanResult.scanType,
        charSet: scanResult.charSet,
        path: scanResult.path,
        timestamp: Date.now()
      };
      
      // 自动处理扫码结果
      if (autoProcess) {
        processedResult = processScannedData(scanResult.result, scanResult.scanType);
      }
      
      // 保存扫码结果
      if (responseDataKey) {
        globalActions.setApiData(responseDataKey, {
          loading: false,
          data: processedResult,
          error: null
        });
      }
      
      // 显示扫码成功提示
      await Taro.showToast({
        title: '扫码成功',
        icon: 'success',
        duration: 1500
      });
      
      return processedResult;
      
    } catch (error) {
      console.error('扫码失败:', error);
      
      // 更新错误状态
      if (responseDataKey) {
        globalActions.setApiData(responseDataKey, {
          loading: false,
          data: null,
          error: error as Error
        });
      }
      
      // 显示错误提示
      try {
        const Taro = require('@tarojs/taro');
        await Taro.showModal({
          title: '扫码失败',
          content: error.message || '未知错误',
          showCancel: false
        });
      } catch (modalError) {
        console.error('显示错误弹框失败:', modalError);
      }
      
      throw error;
    }
  },

  /**
   * 17. depAction - 依赖动作
   * 执行具有复杂依赖关系的动作序列
   */
  depAction: async (params: any, context: EventContext) => {
    const { 
      dependencies = [], 
      actions = [], 
      parallel = false,
      failureStrategy = 'stop', // 'stop' | 'continue' | 'rollback'
      timeout = 30000
    } = params;
    
    console.log('执行依赖动作:', { dependencies: dependencies.length, actions: actions.length, parallel });
    
    const results: any[] = [];
    const dispatcher = EventDispatcher.getInstance();
    
    try {
      // 1. 检查依赖条件
      for (const dep of dependencies) {
        const conditionResult = evaluateExpression(dep.condition, {
          currentComponent: context.currentComponent,
          extraContext: context.extraData
        });
        
        if (!conditionResult) {
          throw new Error(`依赖条件不满足: ${dep.description || dep.condition}`);
        }
      }
      
      // 2. 执行动作序列
      if (parallel) {
        // 并行执行
        const actionPromises = actions.map((action: any, index: number) => 
          dispatcher.dispatch(action, {
            ...context,
            extraData: { 
              ...context.extraData, 
              actionIndex: index,
              parallelMode: true
            }
          }).catch((error) => {
            if (failureStrategy === 'continue') {
              console.warn(`并行动作 ${index} 执行失败，继续执行:`, error);
              return { success: false, error, actionIndex: index };
            }
            throw error;
          })
        );
        
        // 设置超时
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('依赖动作执行超时')), timeout);
        });
        
        const parallelResults = await Promise.race([
          Promise.all(actionPromises),
          timeoutPromise
        ]);
        
        results.push(...parallelResults);
        
      } else {
        // 串行执行
        for (let i = 0; i < actions.length; i++) {
          const action = actions[i];
          
          try {
            const actionResult = await dispatcher.dispatch(action, {
              ...context,
              extraData: { 
                ...context.extraData, 
                actionIndex: i,
                previousResults: results
              }
            });
            
            results.push(actionResult);
            
          } catch (error) {
            console.error(`依赖动作 ${i} 执行失败:`, error);
            
            if (failureStrategy === 'stop') {
              throw error;
            } else if (failureStrategy === 'continue') {
              results.push({ success: false, error, actionIndex: i });
              continue;
            } else if (failureStrategy === 'rollback') {
              // 执行回滚操作 (如果动作支持)
              await rollbackActions(results, dispatcher, context);
              throw error;
            }
          }
        }
      }
      
      console.log('依赖动作执行完成:', results);
      
      return {
        success: true,
        results,
        totalActions: actions.length,
        successCount: results.filter(r => r.success !== false).length,
        failureCount: results.filter(r => r.success === false).length
      };
      
    } catch (error) {
      console.error('依赖动作执行失败:', error);
      
      return {
        success: false,
        error,
        results,
        totalActions: actions.length,
        executedCount: results.length
      };
    }
  }
};

// ========== 导出 ==========

// 单例模式的事件分发器
export const eventDispatcher = EventDispatcher.getInstance();

// 便捷函数
export const dispatchEvent = eventDispatcher.dispatch.bind(eventDispatcher);
export const queueEvent = eventDispatcher.queueExecution.bind(eventDispatcher);

// ========== 工具函数 ==========

/**
 * 获取语言显示名称
 */
function getLanguageName(languageCode: string): string {
  const languageMap: Record<string, string> = {
    'zh-CN': '简体中文',
    'zh-TW': '繁體中文', 
    'en-US': 'English',
    'ja-JP': '日本語',
    'ko-KR': '한국어',
    'fr-FR': 'Français',
    'de-DE': 'Deutsch',
    'es-ES': 'Español'
  };
  
  return languageMap[languageCode] || languageCode;
}

/**
 * 获取当前页面路径
 */
function getCurrentPagePath(): string {
  try {
    const Taro = require('@tarojs/taro');
    const pages = Taro.getCurrentPages();
    if (pages && pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      return `/${currentPage.route}`;
    }
  } catch (error) {
    console.warn('获取当前页面路径失败:', error);
  }
  return '';
}

/**
 * 生成会话ID
 */
function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 根据数据类型获取默认值
 */
function getDefaultValueByType(type?: string): any {
  switch (type) {
    case 'string':
    case 'text':
      return '';
    case 'number':
    case 'integer':
      return 0;
    case 'boolean':
      return false;
    case 'array':
      return [];
    case 'object':
      return {};
    case 'date':
      return null;
    default:
      return '';
  }
}

/**
 * 处理扫码结果数据
 */
function processScannedData(rawData: string, scanType: string): any {
  const result = {
    rawResult: rawData,
    scanType,
    processedData: null as any,
    dataType: 'unknown',
    timestamp: Date.now()
  };

  try {
    // 尝试识别数据类型并解析
    
    // 1. URL类型
    if (rawData.startsWith('http://') || rawData.startsWith('https://')) {
      result.dataType = 'url';
      result.processedData = {
        url: rawData,
        protocol: rawData.split('://')[0],
        host: new URL(rawData).host,
        pathname: new URL(rawData).pathname,
        searchParams: Object.fromEntries(new URL(rawData).searchParams)
      };
    }
    // 2. JSON类型
    else if ((rawData.startsWith('{') && rawData.endsWith('}')) || 
             (rawData.startsWith('[') && rawData.endsWith(']'))) {
      result.dataType = 'json';
      try {
        result.processedData = JSON.parse(rawData);
      } catch (jsonError) {
        result.processedData = { parseError: true, rawData };
      }
    }
    // 3. 微信小程序码
    else if (rawData.includes('pages/')) {
      result.dataType = 'miniprogram';
      result.processedData = {
        page: rawData,
        params: {}
      };
      
      // 解析参数
      if (rawData.includes('?')) {
        const [page, queryString] = rawData.split('?');
        result.processedData.page = page;
        const params = new URLSearchParams(queryString);
        result.processedData.params = Object.fromEntries(params);
      }
    }
    // 4. 邮箱
    else if (rawData.includes('@') && rawData.includes('.')) {
      result.dataType = 'email';
      result.processedData = {
        email: rawData,
        username: rawData.split('@')[0],
        domain: rawData.split('@')[1]
      };
    }
    // 5. 电话号码
    else if (/^[\d\s\-\+\(\)]+$/.test(rawData)) {
      result.dataType = 'phone';
      result.processedData = {
        phone: rawData,
        digits: rawData.replace(/[\s\-\+\(\)]/g, '')
      };
    }
    // 6. 纯文本
    else {
      result.dataType = 'text';
      result.processedData = {
        text: rawData,
        length: rawData.length,
        lines: rawData.split('\n').length
      };
    }
  } catch (error) {
    console.warn('扫码结果处理失败:', error);
    result.dataType = 'error';
    result.processedData = { error: error.message, rawData };
  }

  return result;
}

/**
 * 回滚动作序列 (用于depAction的rollback策略)
 */
async function rollbackActions(
  executedResults: any[], 
  dispatcher: EventDispatcher, 
  context: EventContext
): Promise<void> {
  console.log('开始执行回滚操作...');
  
  // 倒序回滚已执行的动作
  for (let i = executedResults.length - 1; i >= 0; i--) {
    const result = executedResults[i];
    
    try {
      // 如果动作结果中包含回滚信息
      if (result && result.rollback) {
        await dispatcher.dispatch(result.rollback, {
          ...context,
          extraData: { 
            ...context.extraData, 
            rollbackMode: true,
            originalResult: result
          }
        });
        
        console.log(`动作 ${i} 回滚成功`);
      }
    } catch (rollbackError) {
      console.error(`动作 ${i} 回滚失败:`, rollbackError);
      // 回滚失败时继续处理下一个，避免无限递归
    }
  }
  
  console.log('回滚操作完成');
}

export default eventDispatcher;
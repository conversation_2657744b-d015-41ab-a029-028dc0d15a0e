/**
 * 事件系统模块导出 - 统一新旧事件系统
 */

// 原有事件系统 (向后兼容)
export { EventDispatcher } from './event-dispatcher';

// 原有类型导出
export type {
  ActionConfig,
  EventContext as LegacyEventContext, 
  ActionResult
} from './event-dispatcher';

// 新插件系统
export { eventRegistry, type EventPlugin, type EventContext, type EventHandler } from './plugin-system';
export { pluginDispatcher } from './plugin-dispatcher';
export type { ExecutionResult } from './plugin-dispatcher';
export { pluginManager, executeAction } from './plugin-manager';
export { BUILTIN_PLUGINS, registerBuiltinPlugins } from './builtin-plugins';

// 默认导出插件管理器 (新的统一入口) 
import { pluginManager } from './plugin-manager';
export default pluginManager;
/**
 * 内置事件插件
 * 将现有17种事件类型转换为插件形式，保持向后兼容
 */

import type { EventPlugin, EventContext } from './plugin-system';

// ========== 核心动作插件 ==========

/**
 * request - API请求插件
 */
export const requestPlugin: EventPlugin = {
  type: 'request',
  name: 'API请求',
  version: '1.0.0',
  handler: async (params: any, context: EventContext) => {
    const { url, method = 'GET', headers = {}, data, responseDataKey } = params;
    
    try {
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        ...(data && { body: JSON.stringify(data) })
      });

      const responseData = await response.json();
      
      // 保存到全局状态
      if (responseDataKey) {
        context.utils.setValue(`global.apiData.${responseDataKey}`, {
          loading: false,
          data: responseData,
          error: null,
          timestamp: Date.now()
        });
      }

      return responseData;
    } catch (error) {
      if (responseDataKey) {
        context.utils.setValue(`global.apiData.${responseDataKey}`, {
          loading: false,
          data: null,
          error: error.message,
          timestamp: Date.now()
        });
      }
      throw error;
    }
  },
  schema: {
    required: ['url'],
    properties: {
      url: { type: 'string', description: 'API请求地址' },
      method: { 
        type: 'string', 
        description: 'HTTP方法',
        enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
        default: 'GET'
      },
      headers: { type: 'object', description: '请求头', default: {} },
      data: { type: 'object', description: '请求数据' },
      responseDataKey: { type: 'string', description: '响应数据存储key' }
    },
    examples: [
      {
        description: '获取用户信息',
        params: {
          url: '/api/user/profile',
          method: 'GET',
          responseDataKey: 'userProfile'
        }
      }
    ]
  },
  metadata: {
    id: 'builtin.request',
    displayName: 'API请求',
    description: '发送HTTP请求获取或提交数据',
    author: { name: 'System' },
    category: 'integration',
    tags: ['api', 'http', 'data'],
    builtin: true
  },
  security: {
    permissions: ['network:request', 'write:state'],
    timeout: 30000
  }
};

/**
 * setValue - 值设置插件
 */
export const setValuePlugin: EventPlugin = {
  type: 'setValue',
  name: '设置值',
  version: '1.0.0',
  handler: async (params: any, context: EventContext) => {
    const { path, value, entityId: targetEntityId } = params;
    const entityId = targetEntityId || context.entityId;
    
    if (!entityId) {
      throw new Error('setValue需要指定entityId');
    }

    // 更新组件属性
    if (path.startsWith('prop.') || path.startsWith('state.')) {
      context.utils.updateComponent(entityId, { [path]: value });
    } else {
      // 更新全局状态
      context.utils.setValue(path, value);
    }

    return { entityId, path, value };
  },
  schema: {
    required: ['path', 'value'],
    properties: {
      path: { type: 'string', description: '属性路径，如 prop.text 或 state.value' },
      value: { type: 'string', description: '要设置的值' },
      entityId: { type: 'string', description: '目标组件ID，不指定则使用当前组件' }
    },
    examples: [
      {
        description: '设置按钮文本',
        params: {
          path: 'prop.text',
          value: '新的按钮文本'
        }
      }
    ]
  },
  metadata: {
    id: 'builtin.setValue',
    displayName: '设置值',
    description: '设置组件属性或全局状态值',
    author: { name: 'System' },
    category: 'action',
    tags: ['state', 'component', 'update'],
    builtin: true
  },
  security: {
    permissions: ['write:state']
  }
};

/**
 * validate - 验证插件
 */
export const validatePlugin: EventPlugin = {
  type: 'validate',
  name: '数据验证',
  version: '1.0.0',
  handler: async (params: any, context: EventContext) => {
    const { entityId: targetEntityId, rules, showError = true } = params;
    const entityId = targetEntityId || context.entityId;
    
    if (!entityId) {
      throw new Error('validate需要指定entityId');
    }

    const component = context.utils.getComponent(entityId);
    if (!component) {
      throw new Error(`组件 ${entityId} 不存在`);
    }

    const errors: string[] = [];
    const value = component.state?.value || component.prop?.value || '';

    // 执行验证规则
    for (const rule of rules || []) {
      const { type, message, ...config } = rule;
      
      switch (type) {
        case 'required':
          if (!value || String(value).trim() === '') {
            errors.push(message || '此字段为必填项');
          }
          break;
        case 'minLength':
          if (String(value).length < config.value) {
            errors.push(message || `长度不能少于${config.value}个字符`);
          }
          break;
        case 'maxLength':
          if (String(value).length > config.value) {
            errors.push(message || `长度不能超过${config.value}个字符`);
          }
          break;
        case 'pattern':
          if (!new RegExp(config.value).test(String(value))) {
            errors.push(message || '格式不正确');
          }
          break;
        case 'email':
          if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(String(value))) {
            errors.push(message || '请输入有效的邮箱地址');
          }
          break;
      }
    }

    // 更新组件错误状态
    context.utils.updateComponent(entityId, { 
      'state.errors': errors,
      'state.valid': errors.length === 0
    });

    if (errors.length > 0 && showError) {
      // 触发错误显示
      context.utils.updateComponent(entityId, { 'state.showError': true });
    }

    return {
      valid: errors.length === 0,
      errors
    };
  },
  schema: {
    required: [],
    properties: {
      entityId: { type: 'string', description: '要验证的组件ID' },
      rules: { 
        type: 'array', 
        description: '验证规则数组',
        default: []
      },
      showError: { 
        type: 'boolean', 
        description: '是否显示错误信息',
        default: true
      }
    },
    examples: [
      {
        description: '邮箱验证',
        params: {
          rules: [
            { type: 'required', message: '邮箱不能为空' },
            { type: 'email', message: '请输入有效的邮箱地址' }
          ]
        }
      }
    ]
  },
  metadata: {
    id: 'builtin.validate',
    displayName: '数据验证',
    description: '对表单字段进行数据验证',
    author: { name: 'System' },
    category: 'data',
    tags: ['validation', 'form', 'input'],
    builtin: true
  },
  security: {
    permissions: ['read:state', 'write:state']
  }
};

/**
 * linkto - 页面跳转插件
 */
export const linktoPlugin: EventPlugin = {
  type: 'linkto',
  name: '页面跳转',
  version: '1.0.0',
  handler: async (params: any, context: EventContext) => {
    const { url, type = 'navigate', params: urlParams = {} } = params;
    
    // 构建完整URL
    let fullUrl = url;
    if (Object.keys(urlParams).length > 0) {
      const searchParams = new URLSearchParams(urlParams);
      fullUrl = `${url}?${searchParams.toString()}`;
    }

    // 根据类型执行跳转
    switch (type) {
      case 'navigate':
        // Taro路由跳转
        if (typeof window !== 'undefined' && window.location) {
          window.location.href = fullUrl;
        }
        break;
      case 'redirect':
        if (typeof window !== 'undefined' && window.location) {
          window.location.replace(fullUrl);
        }
        break;
      case 'back':
        if (typeof window !== 'undefined' && window.history) {
          window.history.back();
        }
        break;
    }

    return { url: fullUrl, type };
  },
  schema: {
    required: ['url'],
    properties: {
      url: { type: 'string', description: '目标页面URL' },
      type: { 
        type: 'string',
        description: '跳转类型', 
        enum: ['navigate', 'redirect', 'back'],
        default: 'navigate'
      },
      params: { type: 'object', description: 'URL参数', default: {} }
    },
    examples: [
      {
        description: '跳转到详情页',
        params: {
          url: '/pages/detail/index',
          params: { id: '123' }
        }
      }
    ]
  },
  metadata: {
    id: 'builtin.linkto',
    displayName: '页面跳转',
    description: '跳转到指定页面或URL',
    author: { name: 'System' },
    category: 'ui',
    tags: ['navigation', 'route', 'page'],
    builtin: true
  },
  security: {
    permissions: ['dom:access']
  }
};

/**
 * modal - 弹窗插件
 */
export const modalPlugin: EventPlugin = {
  type: 'modal',
  name: '弹窗管理',
  version: '1.0.1', // version bumped
  handler: async (params: any, context: EventContext) => {
    const { action, modalId, title, content, confirmText = '确定', cancelText = '取消' } = params;
    const modalPath = `ui.modals.${modalId}`;

    // 确保基础对象存在
    const existingModalState = context.utils.getValue(modalPath) || {};

    switch (action) {
      case 'show':
        context.utils.setValue(modalPath, {
          ...existingModalState,
          visible: true,
          title,
          content,
          confirmText,
          cancelText
        });
        break;
      case 'hide':
        context.utils.setValue(`${modalPath}.visible`, false);
        break;
      case 'confirm':
        context.utils.setValue(modalPath, {
          ...existingModalState,
          visible: false,
          result: 'confirm'
        });
        break;
      case 'cancel':
        context.utils.setValue(modalPath, {
          ...existingModalState,
          visible: false,
          result: 'cancel'
        });
        break;
    }

    return { action, modalId };
  },
  schema: {
    required: ['action', 'modalId'],
    properties: {
      action: { 
        type: 'string',
        description: '弹窗操作',
        enum: ['show', 'hide', 'confirm', 'cancel']
      },
      modalId: { type: 'string', description: '弹窗ID' },
      title: { type: 'string', description: '弹窗标题' },
      content: { type: 'string', description: '弹窗内容' },
      confirmText: { type: 'string', description: '确认按钮文本', default: '确定' },
      cancelText: { type: 'string', description: '取消按钮文本', default: '取消' }
    },
    examples: [
      {
        description: '显示确认弹窗',
        params: {
          action: 'show',
          modalId: 'confirmDelete',
          title: '确认删除',
          content: '确定要删除这条记录吗？'
        }
      }
    ]
  },
  metadata: {
    id: 'builtin.modal',
    displayName: '弹窗管理',
    description: '显示和管理弹窗对话���',
    author: { name: 'System' },
    category: 'ui',
    tags: ['modal', 'dialog', 'popup'],
    builtin: true
  },
  security: {
    permissions: ['read:state', 'write:state']
  }
};

// ========== 内置插件列表 ==========

export const BUILTIN_PLUGINS: EventPlugin[] = [
  requestPlugin,
  setValuePlugin,  
  validatePlugin,
  linktoPlugin,
  modalPlugin,
  // TODO: 添加其他12种事件类型的插件实现
  // notification, storage, analytics, custom, condition,
  // loop, delay, parallel, sequence, transform, batch, broadcast
];

/**
 * 注册所有内置插件
 */
export async function registerBuiltinPlugins(): Promise<void> {
  const { eventRegistry } = await import('./plugin-system');
  
  console.log('📦 注册内置事件插件...');
  
  for (const plugin of BUILTIN_PLUGINS) {
    try {
      const result = await eventRegistry.register(plugin);
      if (!result.valid) {
        console.error(`内置插件 ${plugin.type} 注册失败:`, result.errors);
      } else {
        if (result.warnings.length > 0) {
          console.warn(`内置插件 ${plugin.type} 注册警告:`, result.warnings);
        }
      }
    } catch (error) {
      console.error(`注册内置插件 ${plugin.type} 时发生错误:`, error);
    }
  }
  
  console.log(`✅ 已注册 ${BUILTIN_PLUGINS.length} 个内置事件插件`);
}

export default BUILTIN_PLUGINS;
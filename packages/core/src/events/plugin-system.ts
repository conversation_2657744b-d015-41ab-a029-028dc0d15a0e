/**
 * 事件插件系统核心架构
 * 将固化的17种事件类型扩展为可插拔架构
 */

// ========== 核心接口定义 ==========

/**
 * 事件处理函数签名
 */
export type EventHandler<T = any, R = any> = (
  params: T,
  context: EventContext
) => Promise<R> | R;

/**
 * 事件执行上下文
 */
export interface EventContext {
  /** 组件实体ID */
  entityId?: string;
  /** 当前状态快照 (只读) */
  state: Readonly<any>;
  /** 工具函数 */
  utils: {
    getValue: (path: string) => any;
    setValue: (path: string, value: any) => void;
    getComponent: (id: string) => any;
    updateComponent: (id: string, updates: Record<string, any>) => void;
  };
  /** 插件间通信 */
  emitEvent: (type: string, params: any) => Promise<any>;
  /** 中断执行链 */
  abort: (reason: string) => void;
}

/**
 * 事件参数验证Schema
 */
export interface EventSchema {
  /** 必需参数 */
  required: string[];
  /** 参数类型定义 */
  properties: Record<string, {
    type: 'string' | 'number' | 'boolean' | 'object' | 'array';
    description?: string;
    default?: any;
    enum?: any[];
    validation?: (value: any) => boolean | string;
  }>;
  /** 示例 */
  examples?: Array<{
    description: string;
    params: any;
  }>;
}

/**
 * 插件元信息
 */
export interface PluginMetadata {
  /** 插件标识 */
  id: string;
  /** 显示名称 */
  displayName: string;
  /** 描述 */
  description: string;
  /** 作者信息 */
  author: {
    name: string;
    email?: string;
    url?: string;
  };
  /** 插件分类 */
  category: 'action' | 'data' | 'ui' | 'integration' | 'utility' | 'custom';
  /** 标签 */
  tags: string[];
  /** 文档链接 */
  docs?: string;
  /** 是否为内置插件 */
  builtin?: boolean;
}

/**
 * 插件安全配置
 */
export interface PluginSecurity {
  /** 权限要求 */
  permissions: Array<
    | 'read:state'      // 读取状态
    | 'write:state'     // 写入状态
    | 'network:request' // 网络请求
    | 'storage:read'    // 读取存储
    | 'storage:write'   // 写入存储
    | 'dom:access'      // DOM操作
    | 'eval:code'       // 代码执行
  >;
  /** 执行超时时间(ms) */
  timeout?: number;
  /** 内存限制(MB) */
  memoryLimit?: number;
  /** 是否需要用户确认 */
  requiresConfirmation?: boolean;
}

/**
 * 事件插件接口
 */
export interface EventPlugin<T = any, R = any> {
  /** 事件类型标识 (唯一) */
  type: string;
  /** 插件名称 */
  name: string;
  /** 版本号 */
  version: string;
  /** 事件处理函数 */
  handler: EventHandler<T, R>;
  /** 参数验证Schema */
  schema: EventSchema;
  /** 依赖的其他插件 */
  dependencies?: string[];
  /** 插件元信息 */
  metadata: PluginMetadata;
  /** 安全配置 */
  security: PluginSecurity;
  /** 生命周期钩子 */
  hooks?: {
    /** 插件注册时 */
    onRegister?: () => void | Promise<void>;
    /** 插件卸载时 */
    onUnregister?: () => void | Promise<void>;
    /** 事件执行前 */
    beforeExecute?: (params: T, context: EventContext) => void | Promise<void>;
    /** 事件执行后 */
    afterExecute?: (result: R, context: EventContext) => void | Promise<void>;
    /** 错误处理 */
    onError?: (error: Error, context: EventContext) => void | Promise<void>;
  };
}

/**
 * 插件验证结果
 */
export interface ValidationResult {
  /** 是否验证通过 */
  valid: boolean;
  /** 错误信息 */
  errors: string[];
  /** 警告信息 */
  warnings: string[];
}


// ========== 事件注册管理器 ==========

/**
 * 事件注册表管理器
 */
export class EventRegistry {
  private plugins = new Map<string, EventPlugin>();
  private dependencies = new Map<string, Set<string>>();
  private executionStats = new Map<string, {
    totalExecutions: number;
    successCount: number;
    errorCount: number;
    averageDuration: number;
  }>();

  /**
   * 注册事件插件
   */
  async register(plugin: EventPlugin): Promise<ValidationResult> {
    // 1. 基础验证
    const validation = this.validatePlugin(plugin);
    if (!validation.valid) {
      return validation;
    }

    // 2. 检查类型冲突
    if (this.plugins.has(plugin.type)) {
      const existing = this.plugins.get(plugin.type)!;
      if (existing.version === plugin.version) {
        return {
          valid: false,
          errors: [`插件类型 '${plugin.type}' 已存在相同版本 ${plugin.version}`],
          warnings: []
        };
      }
      validation.warnings.push(`插件类型 '${plugin.type}' 将被新版本 ${plugin.version} 替换`);
    }

    // 3. 依赖检查
    if (plugin.dependencies) {
      const missingDeps = plugin.dependencies.filter(dep => !this.plugins.has(dep));
      if (missingDeps.length > 0) {
        return {
          valid: false,
          errors: [`缺少依赖插件: ${missingDeps.join(', ')}`],
          warnings: []
        };
      }
    }

    // 4. 安全检查
    if (plugin.security.permissions.includes('eval:code') && !plugin.metadata.builtin) {
      validation.warnings.push('插件请求代码执行权限，存在安全风险');
    }

    // 5. 执行注册
    try {
      this.plugins.set(plugin.type, plugin);
      
      // 记录依赖关系
      if (plugin.dependencies) {
        this.dependencies.set(plugin.type, new Set(plugin.dependencies));
      }

      // 初始化统计
      this.executionStats.set(plugin.type, {
        totalExecutions: 0,
        successCount: 0,
        errorCount: 0,
        averageDuration: 0
      });

      // 执行注册钩子
      if (plugin.hooks?.onRegister) {
        await plugin.hooks.onRegister();
      }

      console.log(`✅ 插件已注册: ${plugin.type} v${plugin.version}`);
    } catch (error) {
      return {
        valid: false,
        errors: [`注册失败: ${error.message}`],
        warnings: []
      };
    }

    return validation;
  }

  /**
   * 卸载事件插件
   */
  async unregister(type: string): Promise<boolean> {
    const plugin = this.plugins.get(type);
    if (!plugin) {
      console.warn(`插件 '${type}' 不存在`);
      return false;
    }

    // 检查是否有其他插件依赖此插件
    const dependents = Array.from(this.dependencies.entries())
      .filter(([_, deps]) => deps.has(type))
      .map(([pluginType]) => pluginType);

    if (dependents.length > 0) {
      console.error(`无法卸载插件 '${type}'，被以下插件依赖: ${dependents.join(', ')}`);
      return false;
    }

    try {
      // 执行卸载钩子
      if (plugin.hooks?.onUnregister) {
        await plugin.hooks.onUnregister();
      }

      // 清理资源
      this.plugins.delete(type);
      this.dependencies.delete(type);
      this.executionStats.delete(type);

      console.log(`🗑️ 插件已卸载: ${type}`);
      return true;
    } catch (error) {
      console.error(`插件卸载失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 获取事件插件
   */
  get(type: string): EventPlugin | null {
    return this.plugins.get(type) || null;
  }

  /**
   * 列出所有插件
   */
  list(category?: PluginMetadata['category']): EventPlugin[] {
    const plugins = Array.from(this.plugins.values());
    if (category) {
      return plugins.filter(plugin => plugin.metadata.category === category);
    }
    return plugins;
  }

  /**
   * 检查插件是否存在
   */
  has(type: string): boolean {
    return this.plugins.has(type);
  }

  /**
   * 获取插件统计信息
   */
  getStats(type?: string): Record<string, any> {
    if (type) {
      return this.executionStats.get(type) || null;
    }
    return Object.fromEntries(this.executionStats);
  }

  /**
   * 更新执行统计
   */
  updateStats(type: string, success: boolean, duration: number): void {
    const stats = this.executionStats.get(type);
    if (stats) {
      stats.totalExecutions++;
      if (success) {
        stats.successCount++;
      } else {
        stats.errorCount++;
      }
      // 计算平均执行时间
      stats.averageDuration = (stats.averageDuration + duration) / 2;
    }
  }

  // ========== 私有方法 ==========

  /**
   * 验证插件
   */
  private validatePlugin(plugin: EventPlugin): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 必需字段检查
    if (!plugin.type || !plugin.type.trim()) {
      errors.push('插件type不能为空');
    }
    if (!plugin.name || !plugin.name.trim()) {
      errors.push('插件name不能为空');
    }
    if (!plugin.version || !this.isValidVersion(plugin.version)) {
      errors.push('插件version格式无效，应为 x.y.z 格式');
    }
    if (!plugin.handler || typeof plugin.handler !== 'function') {
      errors.push('插件handler必须是函数');
    }
    if (!plugin.schema) {
      errors.push('插件schema不能为空');
    }
    if (!plugin.metadata) {
      errors.push('插件metadata不能为空');
    }
    if (!plugin.security) {
      errors.push('插件security配置不能为空');
    }

    // Schema验证
    if (plugin.schema) {
      if (!Array.isArray(plugin.schema.required)) {
        errors.push('schema.required必须是数组');
      }
      if (!plugin.schema.properties || typeof plugin.schema.properties !== 'object') {
        errors.push('schema.properties必须是对象');
      }
    }

    // 元信息验证
    if (plugin.metadata) {
      if (!plugin.metadata.id) {
        errors.push('metadata.id不能为空');
      }
      if (!plugin.metadata.displayName) {
        errors.push('metadata.displayName不能为空');
      }
      if (!plugin.metadata.author || !plugin.metadata.author.name) {
        errors.push('metadata.author.name不能为空');
      }
    }

    // 安全配置验证
    if (plugin.security) {
      if (!Array.isArray(plugin.security.permissions)) {
        errors.push('security.permissions必须是数组');
      }
      if (plugin.security.timeout && plugin.security.timeout < 1000) {
        warnings.push('执行超时时间少于1秒，可能导致插件执行失败');
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 验证版本格式
   */
  private isValidVersion(version: string): boolean {
    return /^\d+\.\d+\.\d+$/.test(version);
  }
}

// ========== 导出单例 ==========

export const eventRegistry = new EventRegistry();

export default eventRegistry;
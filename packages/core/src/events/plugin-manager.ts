/**
 * 插件管理器
 * 提供插件的统一管理、热加载和版本控制
 */

import { eventRegistry, type EventPlugin, type ValidationResult } from './plugin-system';
import { pluginDispatcher } from './plugin-dispatcher';
import type { ExecutionResult } from './plugin-dispatcher';
import { registerBuiltinPlugins } from './builtin-plugins';

// ========== 插件管理器 ==========

export interface PluginLoadResult {
  success: boolean;
  plugin?: EventPlugin;
  error?: string;
}

export interface PluginManagerStats {
  totalPlugins: number;
  builtinPlugins: number;
  customPlugins: number;
  categories: Record<string, number>;
  recentExecutions: Array<{
    type: string;
    timestamp: number;
    success: boolean;
    duration: number;
  }>;
}

/**
 * 插件管理器
 */
export class PluginManager {
  private initialized = false;
  private recentExecutions: Array<{
    type: string;
    timestamp: number;
    success: boolean;
    duration: number;
  }> = [];
  private maxRecentExecutions = 100;

  /**
   * 初始化插件系统
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      console.warn('插件管理器已初始化');
      return;
    }

    try {
      console.log('🚀 初始化插件管理器...');
      
      // 注册所有内置插件
      await registerBuiltinPlugins();
      
      // 标记为已初始化
      this.initialized = true;
      
      console.log('✅ 插件管理器初始化完成');
    } catch (error) {
      console.error('❌ 插件管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 执行事件 (统一入口)
   */
  async executeEvent<T = any, R = any>(
    type: string,
    params: T,
    entityId?: string
  ): Promise<ExecutionResult<R>> {
    if (!this.initialized) {
      await this.initialize();
    }

    const result = await pluginDispatcher.executePluginEvent<T, R>(type, params, entityId);
    
    // 记录执行历史
    this.recordExecution(type, result.success, result.duration);
    
    return result;
  }

  /**
   * 批量执行事件
   */
  async executeBatchEvents(
    events: Array<{ type: string; params: any; entityId?: string }>
  ): Promise<ExecutionResult[]> {
    if (!this.initialized) {
      await this.initialize();
    }

    const results = await pluginDispatcher.executeBatchPluginEvents(events);
    
    // 记录批量执行历史
    results.forEach((result, index) => {
      this.recordExecution(events[index].type, result.success, result.duration);
    });
    
    return results;
  }

  /**
   * 动态加载插件
   */
  async loadPlugin(plugin: EventPlugin): Promise<PluginLoadResult> {
    try {
      const validation = await eventRegistry.register(plugin);
      
      if (validation.valid) {
        console.log(`🔌 插件加载成功: ${plugin.type} v${plugin.version}`);
        return { success: true, plugin };
      } else {
        return {
          success: false,
          error: `插件验证失败: ${validation.errors.join(', ')}`
        };
      }
    } catch (error) {
      return {
        success: false,
        error: `插件加载错误: ${error.message}`
      };
    }
  }

  /**
   * 卸载插件
   */
  async unloadPlugin(type: string): Promise<boolean> {
    const plugin = eventRegistry.get(type);
    if (!plugin) {
      console.warn(`插件 '${type}' 不存在`);
      return false;
    }

    if (plugin.metadata.builtin) {
      console.error(`无法卸载内置插件: ${type}`);
      return false;
    }

    return await eventRegistry.unregister(type);
  }

  /**
   * 热重载插件
   */
  async reloadPlugin(plugin: EventPlugin): Promise<PluginLoadResult> {
    const existingPlugin = eventRegistry.get(plugin.type);
    
    if (existingPlugin && !existingPlugin.metadata.builtin) {
      // 先卸载旧版本
      await this.unloadPlugin(plugin.type);
    }
    
    // 加载新版本
    return await this.loadPlugin(plugin);
  }

  /**
   * 获取插件信息
   */
  getPlugin(type: string): EventPlugin | null {
    return eventRegistry.get(type);
  }

  /**
   * 获取所有插件
   */
  getAllPlugins(category?: EventPlugin['metadata']['category']): EventPlugin[] {
    return eventRegistry.list(category);
  }

  /**
   * 检查插件是否可用
   */
  isPluginAvailable(type: string): boolean {
    return eventRegistry.has(type);
  }

  /**
   * 获取可用的事件类型
   */
  getAvailableEventTypes(): string[] {
    return eventRegistry.list().map(plugin => plugin.type);
  }

  /**
   * 获取插件统计信息
   */
  getStats(): PluginManagerStats {
    const allPlugins = eventRegistry.list();
    const categories: Record<string, number> = {};
    
    let builtinCount = 0;
    let customCount = 0;
    
    allPlugins.forEach(plugin => {
      // 分类统计
      const category = plugin.metadata.category;
      categories[category] = (categories[category] || 0) + 1;
      
      // 内置/自定义统计
      if (plugin.metadata.builtin) {
        builtinCount++;
      } else {
        customCount++;
      }
    });

    return {
      totalPlugins: allPlugins.length,
      builtinPlugins: builtinCount,
      customPlugins: customCount,
      categories,
      recentExecutions: [...this.recentExecutions].reverse() // 最新的在前
    };
  }

  /**
   * 获取插件执行统计
   */
  getPluginStats(type?: string): Record<string, any> {
    return eventRegistry.getStats(type);
  }

  /**
   * 验证插件配置
   */
  validatePluginConfig(plugin: EventPlugin): ValidationResult {
    // 复用eventRegistry的验证逻辑
    const mockRegistry = {
      validatePlugin: (eventRegistry as any).validatePlugin?.bind(eventRegistry)
    };
    
    if (mockRegistry.validatePlugin) {
      return mockRegistry.validatePlugin(plugin);
    }
    
    // 基础验证
    const errors: string[] = [];
    if (!plugin.type) errors.push('type不能为空');
    if (!plugin.name) errors.push('name不能为空');
    if (!plugin.version) errors.push('version不能为空');
    if (!plugin.handler) errors.push('handler不能为空');
    
    return {
      valid: errors.length === 0,
      errors,
      warnings: []
    };
  }

  /**
   * 导出插件配置
   */
  exportPlugin(type: string): EventPlugin | null {
    const plugin = eventRegistry.get(type);
    if (!plugin) return null;
    
    // 返回插件的深拷贝，避免外部修改
    return JSON.parse(JSON.stringify(plugin));
  }

  /**
   * 从配置导入插件
   */
  async importPlugin(config: any): Promise<PluginLoadResult> {
    try {
      // 验证配置格式
      const validation = this.validatePluginConfig(config);
      if (!validation.valid) {
        return {
          success: false,
          error: `配置验证失败: ${validation.errors.join(', ')}`
        };
      }
      
      // 重建handler函数 (如果是序列化的字符串)
      if (typeof config.handler === 'string') {
        config.handler = new Function('params', 'context', `return (${config.handler})(params, context);`);
      }
      
      return await this.loadPlugin(config as EventPlugin);
    } catch (error) {
      return {
        success: false,
        error: `导入失败: ${error.message}`
      };
    }
  }

  /**
   * 重置插件系统
   */
  async reset(): Promise<void> {
    // 卸载所有非内置插件
    const customPlugins = eventRegistry.list().filter(p => !p.metadata.builtin);
    
    for (const plugin of customPlugins) {
      await this.unloadPlugin(plugin.type);
    }
    
    // 清空执行历史
    this.recentExecutions = [];
    
    console.log('🔄 插件系统已重置');
  }

  // ========== 私有方法 ==========

  /**
   * 记录执行历史
   */
  private recordExecution(type: string, success: boolean, duration: number): void {
    this.recentExecutions.push({
      type,
      timestamp: Date.now(),
      success,
      duration
    });
    
    // 限制历史记录数量
    if (this.recentExecutions.length > this.maxRecentExecutions) {
      this.recentExecutions = this.recentExecutions.slice(-this.maxRecentExecutions);
    }
  }
}

// ========== 导出单例 ==========

export const pluginManager = new PluginManager();

// 兼容现有EventDispatcher的入口
export async function executeAction(actionConfig: any, entityId?: string): Promise<any> {
  const { type, params } = actionConfig;
  const result = await pluginManager.executeEvent(type, params, entityId);
  
  if (!result.success) {
    throw new Error(result.error);
  }
  
  return result.data;
}

export default pluginManager;
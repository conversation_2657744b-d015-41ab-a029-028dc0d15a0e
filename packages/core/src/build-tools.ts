/**
 * 构建时工具导出 - 仅在Node.js环境中使用
 * 包含代码生成、解析、代码分割等构建时功能
 */

// 代码生成器
export * from './generators';

// JSON解析器
export * from './parser';

// 构建时工具
export * from './utils/webpack-config-generator';
export * from './utils/performance';
export * from './utils/naming-utils';

// CLI工具 - 移除已删除的文件

// 构建时类型
export type {
  ComponentNode,
  ParsedComponentTree
} from './types/component.types';

export type {
  ActionConfig
} from './events/event-dispatcher';